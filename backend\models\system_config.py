from datetime import datetime
from . import db
import json

class SystemConfig(db.Model):
    """系统配置模型"""
    __tablename__ = 'system_config'
    
    id = db.Column(db.Integer, primary_key=True)
    config_key = db.Column(db.String(100), unique=True, nullable=False)
    config_value = db.Column(db.Text)
    config_type = db.Column(db.String(20), default='string')  # string, int, bool, json
    description = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_value(self):
        """获取配置值（根据类型转换）"""
        if self.config_type == 'int':
            return int(self.config_value) if self.config_value else 0
        elif self.config_type == 'bool':
            return self.config_value.lower() in ['true', '1', 'yes'] if self.config_value else False
        elif self.config_type == 'json':
            return json.loads(self.config_value) if self.config_value else {}
        else:
            return self.config_value or ''
    
    def set_value(self, value):
        """设置配置值（根据类型转换）"""
        if self.config_type == 'json':
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)
    
    def to_dict(self):
        return {
            'id': self.id,
            'config_key': self.config_key,
            'config_value': self.get_value(),
            'config_type': self.config_type,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @staticmethod
    def get_config(key, default=None):
        """获取配置值"""
        config = SystemConfig.query.filter_by(config_key=key).first()
        return config.get_value() if config else default
    
    @staticmethod
    def set_config(key, value, config_type='string', description=''):
        """设置配置值"""
        config = SystemConfig.query.filter_by(config_key=key).first()
        if config:
            config.set_value(value)
            config.updated_at = datetime.utcnow()
        else:
            config = SystemConfig(
                config_key=key,
                config_type=config_type,
                description=description
            )
            config.set_value(value)
            db.session.add(config)
        
        db.session.commit()
        return config

class SystemNotification(db.Model):
    """系统通知模型"""
    __tablename__ = 'system_notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), default='info')  # info, warning, error, success
    
    # 显示设置
    show_on_login = db.Column(db.Boolean, default=False)
    show_as_marquee = db.Column(db.Boolean, default=False)
    
    # 时间设置
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    
    # 状态
    status = db.Column(db.String(20), default='active')  # active, disabled
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def is_active(self):
        """检查通知是否处于活跃状态"""
        if self.status != 'active':
            return False
        
        now = datetime.utcnow()
        
        if self.start_time and now < self.start_time:
            return False
        
        if self.end_time and now > self.end_time:
            return False
        
        return True
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'type': self.type,
            'show_on_login': self.show_on_login,
            'show_as_marquee': self.show_as_marquee,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @staticmethod
    def get_active_notifications(notification_type=None):
        """获取活跃的通知"""
        query = SystemNotification.query.filter_by(status='active')
        
        if notification_type:
            if notification_type == 'login':
                query = query.filter_by(show_on_login=True)
            elif notification_type == 'marquee':
                query = query.filter_by(show_as_marquee=True)
        
        notifications = query.all()
        return [n for n in notifications if n.is_active()]
