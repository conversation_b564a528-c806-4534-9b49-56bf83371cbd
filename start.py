#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业文件共享系统启动脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def check_mysql_connection():
    """检查MySQL连接"""
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        connection.close()
        print("✓ MySQL连接正常")
        return True
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("请确保MySQL服务已启动，用户名密码正确")
        return False

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False

def init_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    try:
        import pymysql

        # 连接MySQL
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )

        cursor = connection.cursor()

        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE 'file_sharing_system'")
        result = cursor.fetchone()

        if not result:
            print("数据库不存在，正在创建...")
            # 读取并执行SQL脚本
            sql_file = Path('database/init_db.sql')
            if sql_file.exists():
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()

                # 分割SQL语句并执行
                sql_statements = sql_content.split(';')
                for statement in sql_statements:
                    statement = statement.strip()
                    if statement:
                        cursor.execute(statement)

                connection.commit()
                print("✓ 数据库初始化完成")
            else:
                print("✗ 数据库初始化脚本不存在")
                return False
        else:
            print("✓ 数据库已存在，跳过初始化")

        cursor.close()
        connection.close()
        return True

    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        'uploads',
        'thumbnails',
        'temp_downloads',
        'logs',
        'search_index',
        'models'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✓ 目录结构创建完成")

def start_application():
    """启动应用"""
    print("\n" + "="*50)
    print("企业文件共享系统启动中...")
    print("="*50)
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_APP'] = 'backend/app.py'
    
    # 启动Flask应用
    try:
        os.chdir('backend')
        subprocess.run([sys.executable, 'app.py'], check=True)
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")

def main():
    """主函数"""
    print("企业文件共享系统 - 初始化和启动")
    print("="*50)
    
    # 检查Python版本
    check_python_version()
    
    # 检查MySQL连接
    if not check_mysql_connection():
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 初始化数据库
    if not init_database():
        return
    
    # 创建目录
    create_directories()
    
    print("\n初始化完成！")
    print("\n默认管理员账户:")
    print("用户名: admin")
    print("密码: admin123")
    print("\n管理员登录地址: http://localhost:5000/admin")
    print("用户登录地址: http://localhost:5000/")
    
    # 询问是否启动
    response = input("\n是否现在启动系统? (y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        start_application()
    else:
        print("您可以稍后运行以下命令启动系统:")
        print("cd backend && python app.py")

if __name__ == '__main__':
    main()
