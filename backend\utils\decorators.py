"""
装饰器工具
"""

from functools import wraps
from flask import jsonify, current_app
from flask_login import current_user

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return jsonify({
                'success': False,
                'message': '请先登录',
                'code': 'NOT_AUTHENTICATED'
            }), 401
        
        if not current_user.is_admin():
            return jsonify({
                'success': False,
                'message': '需要管理员权限',
                'code': 'ADMIN_REQUIRED'
            }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def user_required(f):
    """用户权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return jsonify({
                'success': False,
                'message': '请先登录',
                'code': 'NOT_AUTHENTICATED'
            }), 401
        
        if current_user.is_admin():
            return jsonify({
                'success': False,
                'message': '管理员不能访问用户接口',
                'code': 'INVALID_USER_TYPE'
            }), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def permission_required(permission_name):
    """权限检查装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return jsonify({
                    'success': False,
                    'message': '请先登录',
                    'code': 'NOT_AUTHENTICATED'
                }), 401
            
            if not current_user.has_permission(permission_name):
                return jsonify({
                    'success': False,
                    'message': f'缺少权限: {permission_name}',
                    'code': 'PERMISSION_DENIED'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
