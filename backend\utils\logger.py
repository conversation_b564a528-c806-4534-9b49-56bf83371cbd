"""
日志工具
"""

import os
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime

def setup_logger(app):
    """设置应用日志"""
    
    # 创建日志目录
    log_folder = app.config.get('LOG_FOLDER', 'logs')
    if not os.path.exists(log_folder):
        os.makedirs(log_folder, exist_ok=True)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s [%(name)s] %(message)s'
    )
    
    # 应用日志
    app_log_file = os.path.join(log_folder, 'app.log')
    app_handler = RotatingFileHandler(
        app_log_file,
        maxBytes=app.config.get('MAX_LOG_SIZE', 10*1024*1024),
        backupCount=app.config.get('LOG_BACKUP_COUNT', 5)
    )
    app_handler.setFormatter(formatter)
    app_handler.setLevel(logging.INFO)
    
    # 错误日志
    error_log_file = os.path.join(log_folder, 'error.log')
    error_handler = RotatingFileHandler(
        error_log_file,
        maxBytes=app.config.get('MAX_LOG_SIZE', 10*1024*1024),
        backupCount=app.config.get('LOG_BACKUP_COUNT', 5)
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 访问日志
    access_log_file = os.path.join(log_folder, 'access.log')
    access_handler = RotatingFileHandler(
        access_log_file,
        maxBytes=app.config.get('MAX_LOG_SIZE', 10*1024*1024),
        backupCount=app.config.get('LOG_BACKUP_COUNT', 5)
    )
    access_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(remote_addr)s "%(method)s %(url)s" %(status_code)s'
    ))
    access_handler.setLevel(logging.INFO)
    
    # 添加处理器到应用日志
    if not app.debug:
        app.logger.addHandler(app_handler)
        app.logger.addHandler(error_handler)
        app.logger.setLevel(logging.INFO)
    
    app.logger.info('日志系统初始化完成')

class UserActivityLogger:
    """用户活动日志记录器"""
    
    @staticmethod
    def log_login(user, ip_address, success=True):
        """记录登录活动"""
        status = "成功" if success else "失败"
        message = f"用户登录{status}: {user.username} from {ip_address}"
        logging.getLogger('user_activity').info(message)
    
    @staticmethod
    def log_logout(user, ip_address):
        """记录退出活动"""
        message = f"用户退出: {user.username} from {ip_address}"
        logging.getLogger('user_activity').info(message)
    
    @staticmethod
    def log_file_access(user, file_path, action, ip_address):
        """记录文件访问活动"""
        message = f"文件{action}: {user.username} {action} {file_path} from {ip_address}"
        logging.getLogger('file_activity').info(message)
    
    @staticmethod
    def log_download(user, file_path, file_size, ip_address):
        """记录下载活动"""
        message = f"文件下载: {user.username} 下载 {file_path} ({file_size} bytes) from {ip_address}"
        logging.getLogger('download_activity').info(message)
    
    @staticmethod
    def log_search(user, query, results_count, ip_address):
        """记录搜索活动"""
        message = f"搜索活动: {user.username} 搜索 '{query}' 返回 {results_count} 结果 from {ip_address}"
        logging.getLogger('search_activity').info(message)

class AdminActivityLogger:
    """管理员活动日志记录器"""
    
    @staticmethod
    def log_user_management(admin, action, target_user, ip_address):
        """记录用户管理活动"""
        message = f"用户管理: {admin.username} {action} 用户 {target_user} from {ip_address}"
        logging.getLogger('admin_activity').info(message)
    
    @staticmethod
    def log_system_config(admin, config_item, old_value, new_value, ip_address):
        """记录系统配置变更"""
        message = f"配置变更: {admin.username} 修改 {config_item} 从 '{old_value}' 到 '{new_value}' from {ip_address}"
        logging.getLogger('admin_activity').info(message)
    
    @staticmethod
    def log_file_management(admin, action, file_path, ip_address):
        """记录文件管理活动"""
        message = f"文件管理: {admin.username} {action} {file_path} from {ip_address}"
        logging.getLogger('admin_activity').info(message)
