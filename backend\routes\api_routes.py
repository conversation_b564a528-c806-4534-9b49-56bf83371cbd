"""
通用API路由
"""

from flask import Blueprint, request, jsonify, current_app
from datetime import datetime
import os

api_bp = Blueprint('api', __name__)

@api_bp.route('/system/info', methods=['GET'])
def system_info():
    """获取系统信息"""
    try:
        info = {
            'system_name': '企业文件共享系统',
            'version': 'v1.0.0',
            'status': 'running',
            'server_time': datetime.utcnow().isoformat(),
            'features': [
                '智能搜索',
                '图像识别',
                '批量下载',
                '安全加密',
                '权限管理',
                '实时监控'
            ]
        }
        
        return jsonify({
            'success': True,
            'info': info
        })
        
    except Exception as e:
        current_app.logger.error(f'获取系统信息异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取系统信息失败'
        }), 500

@api_bp.route('/system/announcement', methods=['GET'])
def system_announcement():
    """获取系统公告"""
    try:
        # 这里可以从数据库或配置文件中读取公告
        announcement = "欢迎使用企业文件共享系统！请妥善保管您的登录凭据，如有问题请联系IT部门。"
        
        return jsonify({
            'success': True,
            'announcement': announcement
        })
        
    except Exception as e:
        current_app.logger.error(f'获取系统公告异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取系统公告失败'
        }), 500

@api_bp.route('/system/health', methods=['GET'])
def health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        from models import db
        db.session.execute('SELECT 1')
        
        # 检查关键目录
        from config import Config
        directories = [
            Config.UPLOAD_FOLDER,
            Config.THUMBNAIL_FOLDER,
            Config.LOG_FOLDER
        ]
        
        directory_status = {}
        for directory in directories:
            directory_status[directory] = os.path.exists(directory) and os.access(directory, os.W_OK)
        
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'database': 'connected',
            'directories': directory_status,
            'uptime': 'unknown'  # 可以添加启动时间计算
        }
        
        return jsonify({
            'success': True,
            'health': health_status
        })
        
    except Exception as e:
        current_app.logger.error(f'健康检查异常: {str(e)}')
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@api_bp.route('/system/stats', methods=['GET'])
def system_stats():
    """获取系统统计信息"""
    try:
        from models.user import User
        
        # 基础统计
        total_users = User.query.filter_by(user_type='user').count()
        total_admins = User.query.filter_by(user_type='admin').count()
        active_users = User.query.filter_by(user_type='user', status='active').count()
        
        stats = {
            'users': {
                'total': total_users,
                'active': active_users,
                'admins': total_admins
            },
            'files': {
                'total': 0,  # 待实现
                'size': 0    # 待实现
            },
            'system': {
                'uptime': 'unknown',
                'version': 'v1.0.0',
                'last_backup': 'unknown'
            }
        }
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        current_app.logger.error(f'获取系统统计异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取系统统计失败'
        }), 500
