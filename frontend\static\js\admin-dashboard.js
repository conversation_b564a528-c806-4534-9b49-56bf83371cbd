// 管理员控制台JavaScript
class AdminDashboard {
    constructor() {
        this.currentPage = 'dashboard';
        this.refreshInterval = null;
        this.init();
    }
    
    init() {
        // 检查登录状态
        this.checkAuth();
        
        // 绑定导航事件
        this.bindNavigation();
        
        // 加载初始数据
        this.loadDashboardData();
        
        // 启动实时更新
        this.startRealTimeUpdates();
        
        // 绑定其他事件
        this.bindEvents();
    }
    
    async checkAuth() {
        try {
            const response = await fetch('/api/admin/check-login', {
                method: 'GET',
                credentials: 'include'
            });
            
            if (!response.ok) {
                window.location.href = '/admin/login.html';
                return;
            }
            
            const result = await response.json();
            if (!result.logged_in) {
                window.location.href = '/admin/login.html';
                return;
            }
            
            // 更新管理员信息
            document.getElementById('adminName').textContent = result.admin_name || '管理员';
            
        } catch (error) {
            console.error('检查登录状态失败:', error);
            window.location.href = '/admin/login.html';
        }
    }
    
    bindNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.switchPage(page);
            });
        });
    }
    
    switchPage(page) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');
        
        // 切换页面内容
        document.querySelectorAll('.content-page').forEach(pageEl => {
            pageEl.classList.remove('active');
        });
        document.getElementById(page).classList.add('active');
        
        this.currentPage = page;
        
        // 加载页面数据
        this.loadPageData(page);
    }
    
    async loadPageData(page) {
        switch (page) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'user-management':
                await this.loadUserManagement();
                break;
            case 'file-management':
                await this.loadFileManagement();
                break;
            case 'system-monitor':
                await this.loadSystemMonitor();
                break;
            default:
                console.log(`加载页面: ${page}`);
        }
    }
    
    async loadDashboardData() {
        try {
            const response = await fetch('/api/admin/dashboard-stats', {
                credentials: 'include'
            });
            
            if (response.ok) {
                const data = await response.json();
                this.updateDashboardStats(data);
            }
        } catch (error) {
            console.error('加载控制台数据失败:', error);
        }
    }
    
    updateDashboardStats(data) {
        // 更新统计数据
        document.getElementById('totalUsers').textContent = data.total_users || 0;
        document.getElementById('totalFiles').textContent = data.total_files || 0;
        document.getElementById('totalStorage').textContent = this.formatBytes(data.total_storage || 0);
        document.getElementById('todayDownloads').textContent = data.today_downloads || 0;
        document.getElementById('onlineUsers').textContent = data.online_users || 0;
        
        // 更新实时活动
        this.updateRealtimeActivity(data.recent_activities || []);
        
        // 更新在线用户列表
        this.updateOnlineUsersList(data.online_user_list || []);
    }
    
    updateRealtimeActivity(activities) {
        const container = document.getElementById('realtimeActivity');
        if (activities.length === 0) {
            container.innerHTML = '<div class="activity-item"><span class="activity-desc">暂无活动记录</span></div>';
            return;
        }
        
        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <span class="activity-time">${this.formatTime(activity.time)}</span>
                <span class="activity-desc">${activity.description}</span>
            </div>
        `).join('');
    }
    
    updateOnlineUsersList(users) {
        const container = document.getElementById('onlineUsersList');
        if (users.length === 0) {
            container.innerHTML = '<div class="user-item"><span class="user-name">暂无在线用户</span></div>';
            return;
        }
        
        container.innerHTML = users.map(user => `
            <div class="user-item">
                <span class="user-name">${user.username}</span>
                <span class="user-status">${user.status}</span>
            </div>
        `).join('');
    }
    
    async loadUserManagement() {
        const pageContent = document.getElementById('user-management');
        pageContent.innerHTML = `
            <div class="page-header">
                <div>
                    <h1>用户管理</h1>
                    <p>管理系统用户账户</p>
                </div>
                <button class="btn btn-primary" onclick="showAddUserModal()">+ 添加用户</button>
            </div>
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>正在加载用户数据...</p>
            </div>
        `;
        
        try {
            const response = await fetch('/api/admin/users', {
                credentials: 'include'
            });
            
            if (response.ok) {
                const data = await response.json();
                this.renderUserTable(data.users || []);
            }
        } catch (error) {
            console.error('加载用户数据失败:', error);
        }
    }
    
    renderUserTable(users) {
        const pageContent = document.getElementById('user-management');
        const tableHTML = `
            <div class="page-header">
                <div>
                    <h1>用户管理</h1>
                    <p>管理系统用户账户</p>
                </div>
                <button class="btn btn-primary" onclick="showAddUserModal()">+ 添加用户</button>
            </div>
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>真实姓名</th>
                            <th>邮箱</th>
                            <th>用户组</th>
                            <th>状态</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.real_name || '-'}</td>
                                <td>${user.email || '-'}</td>
                                <td>${user.group_name || '默认组'}</td>
                                <td><span class="status-badge ${user.status}">${this.getStatusText(user.status)}</span></td>
                                <td>${user.last_login ? this.formatDateTime(user.last_login) : '从未登录'}</td>
                                <td>
                                    <button class="btn-small btn-primary" onclick="editUser(${user.id})">编辑</button>
                                    <button class="btn-small btn-secondary" onclick="resetPassword(${user.id})">重置密码</button>
                                    ${user.status === 'active' ? 
                                        `<button class="btn-small btn-danger" onclick="banUser(${user.id})">禁用</button>` :
                                        `<button class="btn-small btn-success" onclick="unbanUser(${user.id})">启用</button>`
                                    }
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        pageContent.innerHTML = tableHTML;
    }
    
    async loadFileManagement() {
        const pageContent = document.getElementById('file-management');
        pageContent.innerHTML = `
            <div class="page-header">
                <div>
                    <h1>文件管理</h1>
                    <p>管理共享文件和目录</p>
                </div>
                <button class="btn btn-primary" onclick="showAddShareModal()">+ 添加共享</button>
            </div>
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>正在加载文件数据...</p>
            </div>
        `;
        
        // 模拟加载文件数据
        setTimeout(() => {
            pageContent.innerHTML = `
                <div class="page-header">
                    <div>
                        <h1>文件管理</h1>
                        <p>管理共享文件和目录</p>
                    </div>
                    <button class="btn btn-primary" onclick="showAddShareModal()">+ 添加共享</button>
                </div>
                <div class="file-management-content">
                    <p>文件管理功能正在开发中...</p>
                </div>
            `;
        }, 1000);
    }
    
    startRealTimeUpdates() {
        // 每30秒更新一次数据
        this.refreshInterval = setInterval(() => {
            if (this.currentPage === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000);
    }
    
    bindEvents() {
        // 绑定窗口关闭事件
        window.addEventListener('beforeunload', () => {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        });
    }
    
    // 工具函数
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        return Math.floor(diff / 86400000) + '天前';
    }
    
    formatDateTime(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    }
    
    getStatusText(status) {
        const statusMap = {
            'active': '正常',
            'banned': '禁用',
            'locked': '锁定'
        };
        return statusMap[status] || status;
    }
}

// 全局函数
function toggleAdminMenu() {
    const dropdown = document.getElementById('adminDropdown');
    dropdown.classList.toggle('show');
}

function showProfile() {
    showModal('个人资料', '<p>个人资料功能正在开发中...</p>');
}

function changePassword() {
    showModal('修改密码', '<p>修改密码功能正在开发中...</p>');
}

function viewLogs() {
    showModal('操作日志', '<p>操作日志功能正在开发中...</p>');
}

async function logout() {
    if (confirm('确定要退出登录吗？')) {
        try {
            const response = await fetch('/api/admin/logout', {
                method: 'POST',
                credentials: 'include'
            });
            
            if (response.ok) {
                window.location.href = '/admin/login.html';
            }
        } catch (error) {
            console.error('退出登录失败:', error);
            window.location.href = '/admin/login.html';
        }
    }
}

function showModal(title, content, footer = null) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalBody').innerHTML = content;
    
    if (footer) {
        document.getElementById('modalFooter').innerHTML = footer;
    }
    
    document.getElementById('modalOverlay').style.display = 'flex';
}

function closeModal() {
    document.getElementById('modalOverlay').style.display = 'none';
}

function showAddUserModal() {
    showModal('添加用户', '<p>添加用户功能正在开发中...</p>');
}

function showAddShareModal() {
    showModal('添加共享', '<p>添加共享功能正在开发中...</p>');
}

// 点击模态框外部关闭
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal-overlay')) {
        closeModal();
    }
    
    // 关闭管理员下拉菜单
    if (!e.target.closest('.admin-menu')) {
        const dropdown = document.getElementById('adminDropdown');
        dropdown.classList.remove('show');
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});
