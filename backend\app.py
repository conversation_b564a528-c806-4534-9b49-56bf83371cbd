#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业文件共享系统 - 主应用程序
作者: 系统开发团队
版本: v1.0.0
"""

import os
import sys
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from datetime import datetime
import logging
from logging.handlers import RotatingFileHandler

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import config
from models import init_app, db
from routes import register_routes
from utils.logger import setup_logger
from utils.security import SecurityManager

def create_app(config_name=None):
    """创建Flask应用实例"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 启用CORS
    CORS(app, supports_credentials=True)
    
    # 初始化数据库
    init_app(app)
    
    # 注册路由
    register_routes(app)
    
    # 设置日志
    setup_logging(app)
    
    # 初始化安全管理器
    SecurityManager.init_app(app)
    
    # 创建必要的目录
    create_directories(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册请求处理器
    register_request_handlers(app)
    
    return app

def create_directories(app):
    """创建必要的目录"""
    directories = [
        app.config['UPLOAD_FOLDER'],
        app.config['THUMBNAIL_FOLDER'],
        app.config['DOWNLOAD_TEMP_FOLDER'],
        app.config['LOG_FOLDER'],
        app.config['SEARCH_INDEX_PATH'],
        app.config['IMAGE_RECOGNITION_MODEL_PATH']
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            app.logger.info(f'创建目录: {directory}')

def setup_logging(app):
    """设置日志系统"""
    if not app.debug and not app.testing:
        # 创建日志目录
        if not os.path.exists(app.config['LOG_FOLDER']):
            os.makedirs(app.config['LOG_FOLDER'])
        
        # 设置文件日志处理器
        file_handler = RotatingFileHandler(
            os.path.join(app.config['LOG_FOLDER'], 'app.log'),
            maxBytes=app.config['MAX_LOG_SIZE'],
            backupCount=app.config['LOG_BACKUP_COUNT']
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('企业文件共享系统启动')

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': '请求的资源不存在',
                'code': 'NOT_FOUND'
            }), 404
        return send_from_directory('../frontend', '404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        app.logger.error(f'服务器内部错误: {str(error)}')
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': '服务器内部错误',
                'code': 'INTERNAL_ERROR'
            }), 500
        return send_from_directory('../frontend', '500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': '访问被拒绝',
                'code': 'FORBIDDEN'
            }), 403
        return send_from_directory('../frontend', '403.html'), 403
    
    @app.errorhandler(413)
    def request_entity_too_large(error):
        return jsonify({
            'success': False,
            'message': '上传文件过大',
            'code': 'FILE_TOO_LARGE'
        }), 413

def register_request_handlers(app):
    """注册请求处理器"""
    
    @app.before_request
    def before_request():
        """请求前处理"""
        # 记录请求信息
        if request.path.startswith('/api/'):
            app.logger.info(f'{request.method} {request.path} - {request.remote_addr}')
        
        # 安全检查
        if not SecurityManager.check_request_security(request):
            return jsonify({
                'success': False,
                'message': '请求被安全策略拒绝',
                'code': 'SECURITY_VIOLATION'
            }), 403
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 设置安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # 记录响应信息
        if request.path.startswith('/api/'):
            app.logger.info(f'{request.method} {request.path} - {response.status_code}')
        
        return response

# 静态文件路由
def register_static_routes(app):
    """注册静态文件路由"""
    
    @app.route('/')
    def index():
        """首页重定向到用户登录"""
        return send_from_directory('../frontend/user', 'login.html')
    
    @app.route('/admin')
    def admin_index():
        """管理员首页重定向到管理员登录"""
        return send_from_directory('../frontend/admin', 'login.html')
    
    @app.route('/admin/<path:filename>')
    def admin_static(filename):
        """管理员静态文件"""
        return send_from_directory('../frontend/admin', filename)
    
    @app.route('/user/<path:filename>')
    def user_static(filename):
        """用户静态文件"""
        return send_from_directory('../frontend/user', filename)
    
    @app.route('/static/<path:filename>')
    def static_files(filename):
        """通用静态文件"""
        return send_from_directory('../frontend/static', filename)

def init_database(app):
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            
            # 创建默认管理员账户
            from models.user import User, UserGroup
            
            # 检查是否已存在管理员
            admin = User.query.filter_by(user_type='admin').first()
            if not admin:
                # 创建默认管理员组
                admin_group = UserGroup(
                    name='管理员组',
                    description='系统管理员组，拥有所有权限',
                    can_read=True,
                    can_download=True,
                    can_upload=True,
                    can_delete=True,
                    can_modify=True,
                    can_search=True,
                    can_use_image_search=True,
                    internal_access=True,
                    external_access=True,
                    max_download_size=0,  # 无限制
                    max_downloads_per_day=0  # 无限制
                )
                db.session.add(admin_group)
                db.session.flush()
                
                # 创建默认管理员账户
                admin = User(
                    username='admin',
                    real_name='系统管理员',
                    user_type='admin',
                    group_id=admin_group.id,
                    status='active'
                )
                admin.set_password('admin123')  # 默认密码，首次登录后需要修改
                db.session.add(admin)
                
                # 创建默认用户组
                default_group = UserGroup(
                    name='默认用户组',
                    description='普通用户默认组',
                    can_read=True,
                    can_download=True,
                    can_upload=False,
                    can_delete=False,
                    can_modify=False,
                    can_search=True,
                    can_use_image_search=True,
                    internal_access=True,
                    external_access=False,
                    max_download_size=100*1024*1024,  # 100MB
                    max_downloads_per_day=50
                )
                db.session.add(default_group)
                
                db.session.commit()
                app.logger.info('数据库初始化完成，创建了默认管理员账户 (admin/admin123)')
            
        except Exception as e:
            app.logger.error(f'数据库初始化失败: {str(e)}')
            db.session.rollback()
            raise

if __name__ == '__main__':
    # 创建应用实例
    app = create_app()
    
    # 注册静态文件路由
    register_static_routes(app)
    
    # 初始化数据库
    init_database(app)
    
    # 启动应用
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    app.logger.info(f'企业文件共享系统启动在端口 {port}')
    app.logger.info(f'管理员登录地址: http://localhost:{port}/admin')
    app.logger.info(f'用户登录地址: http://localhost:{port}/')
    
    app.run(host='0.0.0.0', port=port, debug=debug)
