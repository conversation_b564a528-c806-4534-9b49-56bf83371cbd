"""
用户路由
"""

from flask import Blueprint, request, jsonify, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime, timedelta
import logging

from models import db
from models.user import User, UserGroup
from utils.decorators import user_required
from utils.security import SecurityManager

user_bp = Blueprint('user', __name__)

@user_bp.route('/login', methods=['POST'])
def user_login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '')
        remember = data.get('remember', False)
        
        # 基础验证
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '请输入用户名和密码'
            }), 400
        
        # 查找用户
        user = User.query.filter_by(username=username, user_type='user').first()
        
        if not user:
            current_app.logger.warning(f'用户登录失败 - 用户不存在: {username}')
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
        
        # 验证密码
        if not user.check_password(password):
            # 记录失败登录
            user.failed_login_count += 1
            user.last_failed_login = datetime.utcnow()
            
            # 检查是否需要锁定账户（连续失败5次）
            if user.failed_login_count >= 5:
                user.status = 'locked'
                user.ban_until = datetime.utcnow() + timedelta(minutes=30)  # 锁定30分钟
                db.session.commit()
                
                current_app.logger.warning(f'用户账户被锁定: {username}')
                return jsonify({
                    'success': False,
                    'message': '密码错误次数过多，账户已被锁定30分钟',
                    'code': 'ACCOUNT_LOCKED',
                    'unlock_time': user.ban_until.isoformat()
                }), 401
            
            db.session.commit()
            current_app.logger.warning(f'用户登录失败 - 密码错误: {username}')
            return jsonify({
                'success': False,
                'message': '用户名或密码错误'
            }), 401
        
        # 检查账户状态
        if user.status == 'banned':
            if user.ban_until and user.ban_until > datetime.utcnow():
                return jsonify({
                    'success': False,
                    'message': f'账户已被禁用，解禁时间：{user.ban_until.strftime("%Y-%m-%d %H:%M:%S")}',
                    'code': 'ACCOUNT_BANNED'
                }), 401
            elif not user.ban_until:
                return jsonify({
                    'success': False,
                    'message': '账户已被永久禁用，请联系管理员',
                    'code': 'ACCOUNT_BANNED'
                }), 401
        
        if user.status == 'locked':
            if user.ban_until and user.ban_until > datetime.utcnow():
                return jsonify({
                    'success': False,
                    'message': f'账户已被锁定，解锁时间：{user.ban_until.strftime("%Y-%m-%d %H:%M:%S")}',
                    'code': 'ACCOUNT_LOCKED',
                    'unlock_time': user.ban_until.isoformat()
                }), 401
            else:
                # 自动解锁
                user.status = 'active'
                user.ban_until = None
        
        # 检查网络访问权限
        if not SecurityManager.check_network_access(request, user):
            return jsonify({
                'success': False,
                'message': '当前网络环境不允许访问',
                'code': 'NETWORK_DENIED'
            }), 403
        
        # 登录成功
        login_user(user, remember=remember)
        
        # 更新登录信息
        user.last_login = datetime.utcnow()
        user.login_count += 1
        user.failed_login_count = 0
        if user.status == 'locked':
            user.status = 'active'
            user.ban_until = None
        db.session.commit()
        
        current_app.logger.info(f'用户登录成功: {username}')
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'user_info': {
                'id': user.id,
                'username': user.username,
                'real_name': user.real_name,
                'group_name': user.group.name if user.group else '默认组',
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'permissions': {
                    'can_read': user.group.can_read if user.group else True,
                    'can_download': user.group.can_download if user.group else True,
                    'can_upload': user.group.can_upload if user.group else False,
                    'can_search': user.group.can_search if user.group else True,
                    'can_use_image_search': user.group.can_use_image_search if user.group else True
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f'用户登录异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '登录过程中发生错误'
        }), 500

@user_bp.route('/logout', methods=['POST'])
@login_required
@user_required
def user_logout():
    """用户退出登录"""
    try:
        username = current_user.username
        logout_user()
        session.clear()
        
        current_app.logger.info(f'用户退出登录: {username}')
        
        return jsonify({
            'success': True,
            'message': '退出登录成功'
        })
        
    except Exception as e:
        current_app.logger.error(f'用户退出登录异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '退出登录失败'
        }), 500

@user_bp.route('/check-login', methods=['GET'])
def check_user_login():
    """检查用户登录状态"""
    try:
        if current_user.is_authenticated and not current_user.is_admin():
            return jsonify({
                'logged_in': True,
                'user_name': current_user.real_name or current_user.username,
                'user_id': current_user.id,
                'permissions': {
                    'can_read': current_user.group.can_read if current_user.group else True,
                    'can_download': current_user.group.can_download if current_user.group else True,
                    'can_upload': current_user.group.can_upload if current_user.group else False,
                    'can_search': current_user.group.can_search if current_user.group else True,
                    'can_use_image_search': current_user.group.can_use_image_search if current_user.group else True
                }
            })
        else:
            return jsonify({
                'logged_in': False
            })
            
    except Exception as e:
        current_app.logger.error(f'检查用户登录状态异常: {str(e)}')
        return jsonify({
            'logged_in': False
        })

@user_bp.route('/register-request', methods=['POST'])
def register_request():
    """用户注册申请"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['username', 'real_name', 'email', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=data['username']).first()
        if existing_user:
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        existing_email = User.query.filter_by(email=data['email']).first()
        if existing_email:
            return jsonify({
                'success': False,
                'message': '邮箱已被使用'
            }), 400
        
        # 这里应该创建注册申请记录，等待管理员审核
        # 为了演示，我们直接创建用户（实际项目中应该有审核流程）
        
        # 获取默认用户组
        default_group = UserGroup.query.filter_by(name='默认用户组').first()
        
        # 创建新用户（状态为待激活）
        user = User(
            username=data['username'],
            real_name=data['real_name'],
            email=data['email'],
            user_type='user',
            group_id=default_group.id if default_group else None,
            status='active'  # 实际项目中应该是 'pending' 等待审核
        )
        user.set_password('temp123456')  # 临时密码，审核通过后需要修改
        
        db.session.add(user)
        db.session.commit()
        
        current_app.logger.info(f'新用户注册申请: {user.username} ({user.email})')
        
        return jsonify({
            'success': True,
            'message': '注册申请已提交，请等待管理员审核'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'用户注册申请异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '提交注册申请失败'
        }), 500

@user_bp.route('/profile', methods=['GET'])
@login_required
@user_required
def get_user_profile():
    """获取用户个人信息"""
    try:
        user = current_user
        
        profile_data = {
            'id': user.id,
            'username': user.username,
            'real_name': user.real_name,
            'email': user.email,
            'group_name': user.group.name if user.group else '默认组',
            'status': user.status,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'login_count': user.login_count,
            'download_count': user.download_count,
            'download_size': user.download_size,
            'created_at': user.created_at.isoformat(),
            'permissions': {
                'can_read': user.group.can_read if user.group else True,
                'can_download': user.group.can_download if user.group else True,
                'can_upload': user.group.can_upload if user.group else False,
                'can_delete': user.group.can_delete if user.group else False,
                'can_modify': user.group.can_modify if user.group else False,
                'can_search': user.group.can_search if user.group else True,
                'can_use_image_search': user.group.can_use_image_search if user.group else True,
                'internal_access': user.group.internal_access if user.group else True,
                'external_access': user.group.external_access if user.group else False,
                'max_download_size': user.group.max_download_size if user.group else 100*1024*1024,
                'max_downloads_per_day': user.group.max_downloads_per_day if user.group else 50
            }
        }
        
        return jsonify({
            'success': True,
            'profile': profile_data
        })
        
    except Exception as e:
        current_app.logger.error(f'获取用户个人信息异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取个人信息失败'
        }), 500

@user_bp.route('/change-password', methods=['POST'])
@login_required
@user_required
def change_password():
    """修改密码"""
    try:
        data = request.get_json()
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not old_password or not new_password:
            return jsonify({
                'success': False,
                'message': '请输入原密码和新密码'
            }), 400
        
        if len(new_password) < 6:
            return jsonify({
                'success': False,
                'message': '新密码长度不能少于6位'
            }), 400
        
        # 验证原密码
        if not current_user.check_password(old_password):
            return jsonify({
                'success': False,
                'message': '原密码错误'
            }), 400
        
        # 设置新密码
        current_user.set_password(new_password)
        db.session.commit()
        
        current_app.logger.info(f'用户 {current_user.username} 修改了密码')
        
        return jsonify({
            'success': True,
            'message': '密码修改成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'修改密码异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '修改密码失败'
        }), 500
