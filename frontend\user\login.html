<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 企业文件共享系统</title>
    <link rel="stylesheet" href="../static/css/user-login.css">
    <link rel="icon" href="../static/images/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <img src="../static/images/user-logo.png" alt="用户登录" onerror="this.style.display='none'">
                    <h1>文件共享系统</h1>
                </div>
                <p class="subtitle">安全 · 高效 · 便捷的企业文件共享平台</p>
            </div>
            
            <form id="userLoginForm" class="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <div class="input-wrapper">
                        <input type="text" id="username" name="username" required 
                               placeholder="请输入您的用户名" autocomplete="username">
                        <span class="input-icon">👤</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <div class="input-wrapper">
                        <input type="password" id="password" name="password" required 
                               placeholder="请输入您的密码" autocomplete="current-password">
                        <span class="input-icon">🔒</span>
                        <button type="button" class="toggle-password" onclick="togglePassword()">👁️</button>
                    </div>
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">记住我</label>
                    </div>
                    <a href="#" class="forgot-password" onclick="showForgotPassword()">忘记密码？</a>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="login-btn" id="loginBtn">
                        <span class="btn-text">登录</span>
                        <span class="btn-loading" style="display: none;">登录中...</span>
                    </button>
                </div>
                
                <div class="error-message" id="errorMessage" style="display: none;"></div>
            </form>
            
            <div class="login-footer">
                <div class="features">
                    <div class="feature-item">
                        <span class="feature-icon">🔍</span>
                        <span>智能搜索</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🖼️</span>
                        <span>图像识别</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">⬇️</span>
                        <span>批量下载</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🔐</span>
                        <span>安全加密</span>
                    </div>
                </div>
                
                <div class="register-section">
                    <p>还没有账户？<a href="#" onclick="showRegister()">申请注册</a></p>
                </div>
                
                <div class="system-info">
                    <p>系统版本: v1.0.0 | 技术支持: IT部门</p>
                </div>
            </div>
        </div>
        
        <!-- 背景装饰 -->
        <div class="bg-decoration">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
    </div>
    
    <!-- 系统公告 -->
    <div class="system-announcement" id="systemAnnouncement">
        <div class="announcement-content">
            <span class="announcement-icon">📢</span>
            <span class="announcement-text">欢迎使用企业文件共享系统！请妥善保管您的登录凭据。</span>
        </div>
    </div>
    
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>正在验证用户身份...</p>
    </div>
    
    <!-- 注册模态框 -->
    <div class="modal-overlay" id="registerModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3>用户注册申请</h3>
                <button class="modal-close" onclick="closeRegisterModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="regUsername">用户名</label>
                        <input type="text" id="regUsername" name="username" required placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label for="regRealName">真实姓名</label>
                        <input type="text" id="regRealName" name="real_name" required placeholder="请输入真实姓名">
                    </div>
                    <div class="form-group">
                        <label for="regEmail">邮箱</label>
                        <input type="email" id="regEmail" name="email" required placeholder="请输入邮箱地址">
                    </div>
                    <div class="form-group">
                        <label for="regReason">申请理由</label>
                        <textarea id="regReason" name="reason" rows="3" placeholder="请简述申请使用系统的理由"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeRegisterModal()">取消</button>
                <button class="btn btn-primary" onclick="submitRegister()">提交申请</button>
            </div>
        </div>
    </div>
    
    <script src="../static/js/user-login.js"></script>
</body>
</html>
