#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库重置脚本
用于清理和重新初始化数据库
"""

import sys
import os
from pathlib import Path

def reset_database():
    """重置数据库"""
    print("数据库重置工具")
    print("=" * 30)
    
    # 确认操作
    confirm = input("⚠️  警告：此操作将删除所有数据！是否继续？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("操作已取消")
        return False
    
    try:
        import pymysql
        
        # 连接MySQL
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("正在删除现有数据库...")
        cursor.execute("DROP DATABASE IF EXISTS file_sharing_system")
        
        print("正在重新创建数据库...")
        # 读取并执行SQL脚本
        sql_file = Path('database/init_db.sql')
        if sql_file.exists():
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句并执行
            sql_statements = sql_content.split(';')
            for statement in sql_statements:
                statement = statement.strip()
                if statement:
                    cursor.execute(statement)
            
            connection.commit()
            print("✓ 数据库重置完成")
            print("\n默认管理员账户:")
            print("用户名: admin")
            print("密码: admin123")
        else:
            print("✗ 数据库初始化脚本不存在")
            return False
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库重置失败: {e}")
        return False

if __name__ == '__main__':
    success = reset_database()
    if success:
        print("\n数据库重置成功！现在可以重新启动系统。")
    else:
        print("\n数据库重置失败！")
    
    input("\n按回车键退出...")
