<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业文件共享系统 - 项目概览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.5;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .tech-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .file-structure {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        
        .quick-start {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
        }
        
        .quick-start h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
        }
        
        .start-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .step {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
        }
        
        .step-number {
            background: white;
            color: #27ae60;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 10px;
        }
        
        .login-info {
            background: #e74c3c;
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .login-info h4 {
            margin-bottom: 10px;
        }
        
        .login-links {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }
        
        .login-link {
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            transition: background 0.3s;
        }
        
        .login-link:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 企业文件共享系统</h1>
            <p>安全 · 高效 · 便捷的企业级文件共享解决方案</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🎯 核心功能</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <h3>🔍 双搜索引擎</h3>
                        <p>Everything风格快速搜索 + OpenCV图像识别搜索，支持文件名搜索和相似图片搜索</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔐 权限管理</h3>
                        <p>精细化的用户权限控制，支持内网/外网访问控制，多级用户组管理</p>
                    </div>
                    <div class="feature-card">
                        <h3>📁 文件管理</h3>
                        <p>多盘符、多目录支持，支持JPG、PSD、TIF、AI、EPS、PNG等格式缩略图</p>
                    </div>
                    <div class="feature-card">
                        <h3>⬇️ 批量下载</h3>
                        <p>单文件/批量/文件夹下载，支持加密压缩，可配置下载限制和流量控制</p>
                    </div>
                    <div class="feature-card">
                        <h3>📊 实时监控</h3>
                        <p>用户行为监控、系统状态监控，完整的操作日志和审计功能</p>
                    </div>
                    <div class="feature-card">
                        <h3>🛡️ 安全保护</h3>
                        <p>分离的管理员和用户登录，IP访问控制，防暴力破解，数据加密传输</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🛠️ 技术栈</h2>
                <div class="tech-stack">
                    <div class="tech-item">Python 3.7+</div>
                    <div class="tech-item">Flask</div>
                    <div class="tech-item">MySQL</div>
                    <div class="tech-item">HTML5</div>
                    <div class="tech-item">CSS3</div>
                    <div class="tech-item">JavaScript</div>
                    <div class="tech-item">OpenCV</div>
                    <div class="tech-item">Pillow</div>
                </div>
            </div>
            
            <div class="section">
                <h2>📁 项目结构</h2>
                <div class="file-structure">
Net/
├── backend/                 # 后端代码
│   ├── app.py              # Flask主应用
│   ├── config.py           # 配置文件
│   ├── models/             # 数据库模型
│   ├── routes/             # API路由
│   └── utils/              # 工具函数
├── frontend/               # 前端代码
│   ├── admin/              # 管理员界面
│   ├── user/               # 用户界面
│   └── static/             # 静态资源
├── database/               # 数据库脚本
├── requirements.txt        # Python依赖
├── start.py               # 启动脚本
├── start.bat              # Windows启动脚本
└── README.md              # 项目说明
                </div>
            </div>
        </div>
        
        <div class="quick-start">
            <h3>🚀 快速开始</h3>
            <p>只需几个简单步骤即可启动系统</p>
            
            <div class="start-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>环境准备</h4>
                    <p>安装Python 3.7+和MySQL 5.7+</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>配置数据库</h4>
                    <p>确保MySQL服务启动，用户名root，密码123456</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>一键启动</h4>
                    <p>运行 python start.py 或双击 start.bat</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>开始使用</h4>
                    <p>访问系统并使用默认管理员账户登录</p>
                </div>
            </div>
            
            <div class="login-info">
                <h4>🔑 默认管理员账户</h4>
                <p><strong>用户名:</strong> admin</p>
                <p><strong>密码:</strong> admin123</p>
                
                <div class="login-links">
                    <a href="http://localhost:5000/admin" class="login-link" target="_blank">
                        👤 管理员登录
                    </a>
                    <a href="http://localhost:5000/" class="login-link" target="_blank">
                        👥 用户登录
                    </a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>企业文件共享系统 v1.0.0 | 技术支持: IT部门</p>
            <p>© 2024 保证原创无侵权 | 专为企业内部使用设计</p>
        </div>
    </div>
</body>
</html>
