"""
安全管理工具
"""

import ipaddress
from flask import current_app, request
from datetime import datetime, timedelta

class SecurityManager:
    """安全管理器"""
    
    @staticmethod
    def init_app(app):
        """初始化安全管理器"""
        app.logger.info('安全管理器初始化完成')
    
    @staticmethod
    def check_request_security(request):
        """检查请求安全性"""
        try:
            # 检查请求频率限制
            if not SecurityManager._check_rate_limit(request):
                return False
            
            # 检查IP黑名单
            if SecurityManager._is_ip_blacklisted(request.remote_addr):
                return False
            
            # 检查恶意请求模式
            if SecurityManager._is_malicious_request(request):
                return False
            
            return True
            
        except Exception as e:
            current_app.logger.error(f'安全检查异常: {str(e)}')
            return True  # 异常时允许通过，避免影响正常使用
    
    @staticmethod
    def check_network_access(request, user):
        """检查网络访问权限"""
        try:
            client_ip = request.remote_addr
            
            # 检查是否为内网IP
            is_internal = SecurityManager._is_internal_ip(client_ip)
            
            if user.group:
                # 检查内网访问权限
                if is_internal and not user.group.internal_access:
                    return False
                
                # 检查外网访问权限
                if not is_internal and not user.group.external_access:
                    return False
            
            return True
            
        except Exception as e:
            current_app.logger.error(f'网络访问检查异常: {str(e)}')
            return True  # 异常时允许通过
    
    @staticmethod
    def _check_rate_limit(request):
        """检查请求频率限制"""
        # 这里可以实现基于IP的请求频率限制
        # 简单实现，实际项目中可以使用Redis等
        return True
    
    @staticmethod
    def _is_ip_blacklisted(ip):
        """检查IP是否在黑名单中"""
        # 这里可以实现IP黑名单检查
        blacklisted_ips = []  # 可以从数据库或配置文件读取
        return ip in blacklisted_ips
    
    @staticmethod
    def _is_malicious_request(request):
        """检查是否为恶意请求"""
        # 检查常见的恶意请求模式
        malicious_patterns = [
            'script',
            'javascript:',
            '<script',
            'eval(',
            'union select',
            'drop table',
            '../',
            '..\\',
        ]
        
        # 检查URL和参数
        full_url = request.url.lower()
        for pattern in malicious_patterns:
            if pattern in full_url:
                current_app.logger.warning(f'检测到恶意请求模式: {pattern} in {request.url}')
                return True
        
        # 检查POST数据
        if request.is_json:
            try:
                data_str = str(request.get_json()).lower()
                for pattern in malicious_patterns:
                    if pattern in data_str:
                        current_app.logger.warning(f'检测到恶意请求数据: {pattern}')
                        return True
            except:
                pass
        
        return False
    
    @staticmethod
    def _is_internal_ip(ip):
        """检查是否为内网IP"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # 检查私有网络地址
            if ip_obj.is_private:
                return True
            
            # 检查回环地址
            if ip_obj.is_loopback:
                return True
            
            # 检查链路本地地址
            if ip_obj.is_link_local:
                return True
            
            return False
            
        except ValueError:
            # 无效IP地址，默认为外网
            return False
    
    @staticmethod
    def log_security_event(event_type, description, user_id=None, ip_address=None):
        """记录安全事件"""
        try:
            current_app.logger.warning(f'安全事件 [{event_type}]: {description} - 用户ID: {user_id}, IP: {ip_address}')
            
            # 这里可以将安全事件记录到专门的安全日志表中
            # 实际项目中应该有专门的安全事件记录机制
            
        except Exception as e:
            current_app.logger.error(f'记录安全事件失败: {str(e)}')

class PasswordPolicy:
    """密码策略"""
    
    @staticmethod
    def validate_password(password):
        """验证密码强度"""
        errors = []
        
        if len(password) < 6:
            errors.append('密码长度不能少于6位')
        
        if len(password) > 128:
            errors.append('密码长度不能超过128位')
        
        # 可以添加更多密码策略
        # if not re.search(r'[A-Z]', password):
        #     errors.append('密码必须包含大写字母')
        
        # if not re.search(r'[a-z]', password):
        #     errors.append('密码必须包含小写字母')
        
        # if not re.search(r'\d', password):
        #     errors.append('密码必须包含数字')
        
        return len(errors) == 0, errors
    
    @staticmethod
    def is_common_password(password):
        """检查是否为常见密码"""
        common_passwords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'root', '111111', '000000'
        ]
        
        return password.lower() in common_passwords
