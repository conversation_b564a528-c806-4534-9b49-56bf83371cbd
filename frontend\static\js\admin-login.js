// 管理员登录页面JavaScript
class AdminLogin {
    constructor() {
        this.form = document.getElementById('adminLoginForm');
        this.loginBtn = document.getElementById('loginBtn');
        this.errorMessage = document.getElementById('errorMessage');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        
        this.init();
    }
    
    init() {
        // 绑定表单提交事件
        this.form.addEventListener('submit', (e) => this.handleLogin(e));
        
        // 绑定回车键登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin(e);
            }
        });
        
        // 清除错误信息
        const inputs = this.form.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => this.clearError());
        });
        
        // 检查是否已登录
        this.checkLoginStatus();
    }
    
    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(this.form);
        const loginData = {
            username: formData.get('username').trim(),
            password: formData.get('password'),
            remember: formData.get('remember') === 'on'
        };
        
        // 基础验证
        if (!this.validateForm(loginData)) {
            return;
        }
        
        try {
            this.setLoading(true);
            
            const response = await fetch('/api/admin/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(loginData)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                this.showSuccess('登录成功，正在跳转...');
                
                // 保存登录状态
                if (loginData.remember) {
                    localStorage.setItem('admin_remember', 'true');
                }
                
                // 跳转到管理后台
                setTimeout(() => {
                    window.location.href = '/admin/dashboard';
                }, 1000);
                
            } else {
                this.showError(result.message || '登录失败，请检查账号密码');
            }
            
        } catch (error) {
            console.error('登录请求失败:', error);
            this.showError('网络连接失败，请检查网络后重试');
        } finally {
            this.setLoading(false);
        }
    }
    
    validateForm(data) {
        if (!data.username) {
            this.showError('请输入管理员账号');
            document.getElementById('username').focus();
            return false;
        }
        
        if (data.username.length < 3) {
            this.showError('管理员账号长度不能少于3位');
            document.getElementById('username').focus();
            return false;
        }
        
        if (!data.password) {
            this.showError('请输入登录密码');
            document.getElementById('password').focus();
            return false;
        }
        
        if (data.password.length < 6) {
            this.showError('密码长度不能少于6位');
            document.getElementById('password').focus();
            return false;
        }
        
        return true;
    }
    
    setLoading(loading) {
        if (loading) {
            this.loginBtn.disabled = true;
            this.loginBtn.querySelector('.btn-text').style.display = 'none';
            this.loginBtn.querySelector('.btn-loading').style.display = 'inline';
            this.loadingOverlay.style.display = 'flex';
        } else {
            this.loginBtn.disabled = false;
            this.loginBtn.querySelector('.btn-text').style.display = 'inline';
            this.loginBtn.querySelector('.btn-loading').style.display = 'none';
            this.loadingOverlay.style.display = 'none';
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.errorMessage.style.background = '#fee';
        this.errorMessage.style.color = '#c33';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            this.clearError();
        }, 5000);
    }
    
    showSuccess(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.errorMessage.style.background = '#efe';
        this.errorMessage.style.color = '#3c3';
    }
    
    clearError() {
        this.errorMessage.style.display = 'none';
    }
    
    async checkLoginStatus() {
        try {
            const response = await fetch('/api/admin/check-login', {
                method: 'GET',
                credentials: 'include'
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.logged_in) {
                    // 已登录，直接跳转
                    window.location.href = '/admin/dashboard';
                }
            }
        } catch (error) {
            // 忽略检查登录状态的错误
            console.log('检查登录状态失败:', error);
        }
    }
}

// 密码显示/隐藏切换
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.textContent = '🙈';
    } else {
        passwordInput.type = 'password';
        toggleBtn.textContent = '👁️';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new AdminLogin();
});

// 防止页面被嵌入iframe
if (window.top !== window.self) {
    window.top.location = window.location;
}
