#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
用于验证系统各组件是否正常工作
"""

import sys
import os
import subprocess
import time
import requests
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("1. 测试Python版本...")
    if sys.version_info >= (3, 7):
        print(f"   ✓ Python版本: {sys.version}")
        return True
    else:
        print(f"   ✗ Python版本过低: {sys.version}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("2. 测试依赖包...")
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'pymysql',
        'pillow',
        'opencv-python'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✓ {package}")
        except ImportError:
            print(f"   ✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"   缺少依赖包: {', '.join(missing_packages)}")
        print("   请运行: pip install -r requirements.txt")
        return False
    
    return True

def test_mysql_connection():
    """测试MySQL连接"""
    print("3. 测试MySQL连接...")
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        connection.close()
        print("   ✓ MySQL连接正常")
        return True
    except Exception as e:
        print(f"   ✗ MySQL连接失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("4. 测试文件结构...")
    required_files = [
        'backend/app.py',
        'backend/config.py',
        'backend/models/__init__.py',
        'backend/routes/__init__.py',
        'frontend/admin/login.html',
        'frontend/user/login.html',
        'database/init_db.sql',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✓ {file_path}")
        else:
            print(f"   ✗ {file_path} 不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"   缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def test_directories():
    """测试目录创建"""
    print("5. 测试目录创建...")
    required_dirs = [
        'uploads',
        'thumbnails',
        'temp_downloads',
        'logs',
        'search_index',
        'models'
    ]
    
    for directory in required_dirs:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"   ✓ {directory}")
        except Exception as e:
            print(f"   ✗ 创建目录 {directory} 失败: {e}")
            return False
    
    return True

def test_database_init():
    """测试数据库初始化"""
    print("6. 测试数据库初始化...")
    try:
        import pymysql
        
        # 连接MySQL
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE 'file_sharing_system'")
        result = cursor.fetchone()
        
        if result:
            print("   ✓ 数据库 file_sharing_system 已存在")
        else:
            print("   ! 数据库不存在，需要初始化")
            
            # 读取并执行SQL脚本
            sql_file = Path('database/init_db.sql')
            if sql_file.exists():
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                # 分割SQL语句并执行
                sql_statements = sql_content.split(';')
                for statement in sql_statements:
                    statement = statement.strip()
                    if statement:
                        cursor.execute(statement)
                
                connection.commit()
                print("   ✓ 数据库初始化完成")
            else:
                print("   ✗ 数据库初始化脚本不存在")
                return False
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ✗ 数据库初始化失败: {e}")
        return False

def test_flask_app():
    """测试Flask应用启动"""
    print("7. 测试Flask应用...")
    
    # 启动Flask应用（后台进程）
    try:
        os.chdir('backend')
        process = subprocess.Popen(
            [sys.executable, 'app.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待应用启动
        time.sleep(5)
        
        # 测试应用是否响应
        try:
            response = requests.get('http://localhost:5000/api/system/info', timeout=5)
            if response.status_code == 200:
                print("   ✓ Flask应用启动成功")
                result = True
            else:
                print(f"   ✗ Flask应用响应异常: {response.status_code}")
                result = False
        except requests.exceptions.RequestException as e:
            print(f"   ✗ 无法连接到Flask应用: {e}")
            result = False
        
        # 终止进程
        process.terminate()
        process.wait()
        
        os.chdir('..')
        return result
        
    except Exception as e:
        print(f"   ✗ Flask应用启动失败: {e}")
        os.chdir('..')
        return False

def run_all_tests():
    """运行所有测试"""
    print("企业文件共享系统 - 系统测试")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_dependencies,
        test_mysql_connection,
        test_file_structure,
        test_directories,
        test_database_init,
        test_flask_app
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！系统可以正常启动。")
        print("\n启动命令:")
        print("cd backend && python app.py")
        print("\n访问地址:")
        print("管理员: http://localhost:5000/admin")
        print("用户: http://localhost:5000/")
        return True
    else:
        print("✗ 部分测试失败，请检查上述错误信息。")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
