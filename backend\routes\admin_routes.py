"""
管理员路由
"""

from flask import Blueprint, request, jsonify, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime, timedelta
import logging

from models import db
from models.user import User, UserGroup
from utils.decorators import admin_required
from utils.security import SecurityManager

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/login', methods=['POST'])
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '')
        remember = data.get('remember', False)
        
        # 基础验证
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '请输入用户名和密码'
            }), 400
        
        # 查找管理员用户
        user = User.query.filter_by(username=username, user_type='admin').first()
        
        if not user:
            current_app.logger.warning(f'管理员登录失败 - 用户不存在: {username}')
            return jsonify({
                'success': False,
                'message': '管理员账号不存在'
            }), 401
        
        # 验证密码
        if not user.check_password(password):
            # 记录失败登录
            user.failed_login_count += 1
            user.last_failed_login = datetime.utcnow()
            db.session.commit()
            
            current_app.logger.warning(f'管理员登录失败 - 密码错误: {username}')
            return jsonify({
                'success': False,
                'message': '密码错误'
            }), 401
        
        # 检查账户状态
        if not user.is_active_user():
            return jsonify({
                'success': False,
                'message': '账户已被禁用或锁定'
            }), 401
        
        # 登录成功
        login_user(user, remember=remember)
        
        # 更新登录信息
        user.last_login = datetime.utcnow()
        user.login_count += 1
        user.failed_login_count = 0
        db.session.commit()
        
        current_app.logger.info(f'管理员登录成功: {username}')
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'admin_info': {
                'id': user.id,
                'username': user.username,
                'real_name': user.real_name,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
        })
        
    except Exception as e:
        current_app.logger.error(f'管理员登录异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '登录过程中发生错误'
        }), 500

@admin_bp.route('/logout', methods=['POST'])
@login_required
@admin_required
def admin_logout():
    """管理员退出登录"""
    try:
        username = current_user.username
        logout_user()
        session.clear()
        
        current_app.logger.info(f'管理员退出登录: {username}')
        
        return jsonify({
            'success': True,
            'message': '退出登录成功'
        })
        
    except Exception as e:
        current_app.logger.error(f'管理员退出登录异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '退出登录失败'
        }), 500

@admin_bp.route('/check-login', methods=['GET'])
def check_admin_login():
    """检查管理员登录状态"""
    try:
        if current_user.is_authenticated and current_user.is_admin():
            return jsonify({
                'logged_in': True,
                'admin_name': current_user.real_name or current_user.username,
                'admin_id': current_user.id
            })
        else:
            return jsonify({
                'logged_in': False
            })
            
    except Exception as e:
        current_app.logger.error(f'检查管理员登录状态异常: {str(e)}')
        return jsonify({
            'logged_in': False
        })

@admin_bp.route('/dashboard-stats', methods=['GET'])
@login_required
@admin_required
def dashboard_stats():
    """获取控制台统计数据"""
    try:
        # 统计用户数量
        total_users = User.query.filter_by(user_type='user').count()
        
        # 统计在线用户（最近5分钟有活动的用户）
        five_minutes_ago = datetime.utcnow() - timedelta(minutes=5)
        online_users = User.query.filter(
            User.last_login >= five_minutes_ago,
            User.user_type == 'user'
        ).count()
        
        # 模拟其他统计数据（实际项目中需要从相应的表中查询）
        stats = {
            'total_users': total_users,
            'total_files': 0,  # 待实现
            'total_storage': 0,  # 待实现
            'today_downloads': 0,  # 待实现
            'online_users': online_users,
            'recent_activities': [
                {
                    'time': datetime.utcnow().isoformat(),
                    'description': '系统运行正常'
                }
            ],
            'online_user_list': []  # 待实现
        }
        
        return jsonify(stats)
        
    except Exception as e:
        current_app.logger.error(f'获取控制台统计数据异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取统计数据失败'
        }), 500

@admin_bp.route('/users', methods=['GET'])
@login_required
@admin_required
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 查询用户（排除管理员）
        users_query = User.query.filter_by(user_type='user')
        
        # 分页
        users_pagination = users_query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users_data = []
        for user in users_pagination.items:
            users_data.append(user.to_dict())
        
        return jsonify({
            'success': True,
            'users': users_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': users_pagination.total,
                'pages': users_pagination.pages
            }
        })
        
    except Exception as e:
        current_app.logger.error(f'获取用户列表异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取用户列表失败'
        }), 500

@admin_bp.route('/users', methods=['POST'])
@login_required
@admin_required
def create_user():
    """创建用户"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['username', 'password', 'real_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=data['username']).first()
        if existing_user:
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 创建新用户
        user = User(
            username=data['username'],
            real_name=data['real_name'],
            email=data.get('email'),
            user_type='user',
            group_id=data.get('group_id'),
            status='active'
        )
        user.set_password(data['password'])
        
        db.session.add(user)
        db.session.commit()
        
        current_app.logger.info(f'管理员 {current_user.username} 创建了用户: {user.username}')
        
        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建用户异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '创建用户失败'
        }), 500

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@login_required
@admin_required
def update_user(user_id):
    """更新用户信息"""
    try:
        user = User.query.get_or_404(user_id)
        
        if user.user_type == 'admin':
            return jsonify({
                'success': False,
                'message': '不能修改管理员账户'
            }), 403
        
        data = request.get_json()
        
        # 更新用户信息
        if 'real_name' in data:
            user.real_name = data['real_name']
        if 'email' in data:
            user.email = data['email']
        if 'group_id' in data:
            user.group_id = data['group_id']
        if 'status' in data:
            user.status = data['status']
        
        db.session.commit()
        
        current_app.logger.info(f'管理员 {current_user.username} 更新了用户: {user.username}')
        
        return jsonify({
            'success': True,
            'message': '用户信息更新成功',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新用户异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '更新用户失败'
        }), 500

@admin_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@login_required
@admin_required
def reset_user_password(user_id):
    """重置用户密码"""
    try:
        user = User.query.get_or_404(user_id)
        
        if user.user_type == 'admin':
            return jsonify({
                'success': False,
                'message': '不能重置管理员密码'
            }), 403
        
        data = request.get_json()
        new_password = data.get('new_password', 'password123')  # 默认密码
        
        user.set_password(new_password)
        db.session.commit()
        
        current_app.logger.info(f'管理员 {current_user.username} 重置了用户 {user.username} 的密码')
        
        return jsonify({
            'success': True,
            'message': '密码重置成功',
            'new_password': new_password
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'重置用户密码异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': '重置密码失败'
        }), 500
