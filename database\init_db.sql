-- 企业文件共享系统数据库初始化脚本
-- 数据库: file_sharing_system
-- 字符集: utf8mb4

-- 创建数据库
CREATE DATABASE IF NOT EXISTS file_sharing_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE file_sharing_system;

-- 用户组表
CREATE TABLE IF NOT EXISTS user_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '组名',
    description TEXT COMMENT '描述',
    
    -- 基础权限
    can_read BOOLEAN DEFAULT TRUE COMMENT '读取权限',
    can_download BOOLEAN DEFAULT TRUE COMMENT '下载权限',
    can_upload BOOLEAN DEFAULT FALSE COMMENT '上传权限',
    can_delete BOOLEAN DEFAULT FALSE COMMENT '删除权限',
    can_modify BOOLEAN DEFAULT FALSE COMMENT '修改权限',
    
    -- 搜索权限
    can_search BOOLEAN DEFAULT TRUE COMMENT '搜索权限',
    can_use_image_search BOOLEAN DEFAULT TRUE COMMENT '图像搜索权限',
    
    -- 网络访问权限
    internal_access BOOLEAN DEFAULT TRUE COMMENT '内网访问权限',
    external_access BOOLEAN DEFAULT FALSE COMMENT '外网访问权限',
    
    -- 下载限制
    max_download_size BIGINT DEFAULT 104857600 COMMENT '最大下载大小(字节)',
    max_downloads_per_day INT DEFAULT 50 COMMENT '每日最大下载次数',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组表';

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(120) UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(100) COMMENT '真实姓名',
    
    -- 用户类型：admin(管理员), user(普通用户)
    user_type VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '用户类型',
    
    -- 用户组ID
    group_id INT COMMENT '用户组ID',
    
    -- 状态：active(活跃), banned(禁用), locked(锁定)
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态',
    
    -- 禁用到期时间
    ban_until TIMESTAMP NULL COMMENT '禁用到期时间',
    
    -- 登录相关
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    failed_login_count INT DEFAULT 0 COMMENT '失败登录次数',
    last_failed_login TIMESTAMP NULL COMMENT '最后失败登录时间',
    
    -- 下载统计
    download_count INT DEFAULT 0 COMMENT '下载次数',
    download_size BIGINT DEFAULT 0 COMMENT '下载总大小(字节)',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status),
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 共享目录表
CREATE TABLE IF NOT EXISTS shared_directories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '共享名称',
    path VARCHAR(500) NOT NULL COMMENT '实际路径',
    description TEXT COMMENT '描述',
    
    -- 访问权限
    internal_access BOOLEAN DEFAULT TRUE COMMENT '内网访问',
    external_access BOOLEAN DEFAULT FALSE COMMENT '外网访问',
    
    -- 功能权限
    allow_read BOOLEAN DEFAULT TRUE COMMENT '允许读取',
    allow_download BOOLEAN DEFAULT TRUE COMMENT '允许下载',
    allow_upload BOOLEAN DEFAULT FALSE COMMENT '允许上传',
    allow_delete BOOLEAN DEFAULT FALSE COMMENT '允许删除',
    allow_modify BOOLEAN DEFAULT FALSE COMMENT '允许修改',
    
    -- 搜索设置
    enable_search BOOLEAN DEFAULT TRUE COMMENT '启用搜索',
    enable_image_search BOOLEAN DEFAULT TRUE COMMENT '启用图像搜索',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态: active, disabled',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='共享目录表';

-- 文件信息表
CREATE TABLE IF NOT EXISTS file_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    shared_dir_id INT NOT NULL COMMENT '共享目录ID',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件相对路径',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    file_extension VARCHAR(20) COMMENT '文件扩展名',
    
    -- 文件哈希
    file_hash VARCHAR(64) COMMENT '文件MD5哈希',
    
    -- 图片相关
    is_image BOOLEAN DEFAULT FALSE COMMENT '是否为图片',
    image_width INT COMMENT '图片宽度',
    image_height INT COMMENT '图片高度',
    has_thumbnail BOOLEAN DEFAULT FALSE COMMENT '是否有缩略图',
    
    -- 索引状态
    indexed BOOLEAN DEFAULT FALSE COMMENT '是否已索引',
    image_analyzed BOOLEAN DEFAULT FALSE COMMENT '是否已图像分析',
    
    -- 时间信息
    file_mtime TIMESTAMP COMMENT '文件修改时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_shared_dir (shared_dir_id),
    INDEX idx_file_name (file_name),
    INDEX idx_file_type (file_type),
    INDEX idx_is_image (is_image),
    INDEX idx_file_hash (file_hash),
    FOREIGN KEY (shared_dir_id) REFERENCES shared_directories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';

-- 用户日志表
CREATE TABLE IF NOT EXISTS user_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    
    -- 文件相关
    file_id BIGINT COMMENT '文件ID',
    file_path VARCHAR(1000) COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小',
    
    -- 搜索相关
    search_query VARCHAR(500) COMMENT '搜索关键词',
    search_results_count INT COMMENT '搜索结果数量',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES file_info(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户日志表';

-- 下载记录表
CREATE TABLE IF NOT EXISTS download_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    file_id BIGINT COMMENT '文件ID',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小',
    download_type VARCHAR(20) NOT NULL COMMENT '下载类型: single, batch, folder',
    
    -- 加密信息
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    encryption_password VARCHAR(255) COMMENT '加密密码',
    
    -- 网络信息
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'completed' COMMENT '状态: completed, failed, cancelled',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_file_id (file_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES file_info(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='下载记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型: string, int, bool, json',
    description TEXT COMMENT '配置描述',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 系统通知表
CREATE TABLE IF NOT EXISTS system_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type VARCHAR(20) DEFAULT 'info' COMMENT '通知类型: info, warning, error, success',
    
    -- 显示设置
    show_on_login BOOLEAN DEFAULT FALSE COMMENT '登录时显示',
    show_as_marquee BOOLEAN DEFAULT FALSE COMMENT '滚动显示',
    
    -- 时间设置
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态: active, disabled',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统通知表';

-- 插入默认数据
INSERT INTO user_groups (name, description, can_read, can_download, can_upload, can_delete, can_modify, can_search, can_use_image_search, internal_access, external_access, max_download_size, max_downloads_per_day) VALUES
('管理员组', '系统管理员组，拥有所有权限', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, 0, 0),
('默认用户组', '普通用户默认组', TRUE, TRUE, FALSE, FALSE, FALSE, TRUE, TRUE, TRUE, FALSE, 104857600, 50),
('高级用户组', '高级用户组，拥有上传权限', TRUE, TRUE, TRUE, FALSE, FALSE, TRUE, TRUE, TRUE, TRUE, 524288000, 100),
('只读用户组', '只读用户组，仅能浏览和搜索', TRUE, FALSE, FALSE, FALSE, FALSE, TRUE, TRUE, TRUE, FALSE, 0, 0);

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('system_name', '企业文件共享系统', 'string', '系统名称'),
('system_version', 'v1.0.0', 'string', '系统版本'),
('max_upload_size', '1073741824', 'int', '最大上传文件大小(字节)'),
('enable_registration', 'false', 'bool', '是否允许用户注册'),
('enable_external_access', 'true', 'bool', '是否允许外网访问'),
('default_user_group', '2', 'int', '默认用户组ID'),
('session_timeout', '28800', 'int', '会话超时时间(秒)'),
('enable_image_recognition', 'true', 'bool', '是否启用图像识别'),
('enable_file_encryption', 'true', 'bool', '是否启用文件加密下载'),
('encryption_threshold', '5', 'int', '加密下载阈值(下载次数)');

-- 插入默认通知
INSERT INTO system_notifications (title, content, type, show_as_marquee, status) VALUES
('系统欢迎', '欢迎使用企业文件共享系统！请妥善保管您的登录凭据。', 'info', TRUE, 'active'),
('安全提醒', '为了您的账户安全，请定期修改密码，不要与他人共享账户信息。', 'warning', FALSE, 'active');
