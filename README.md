# 企业文件共享系统

一个功能完整的企业级文件共享系统，支持智能搜索、图像识别、权限管理、实时监控等功能。

## 🚀 主要功能

### 核心功能
- **双搜索引擎**: Everything风格快速搜索 + OpenCV图像识别搜索
- **权限管理**: 精细化的用户权限控制，支持内网/外网访问控制
- **文件管理**: 多盘符、多目录支持，支持各种文件格式
- **批量下载**: 单文件/批量/文件夹下载，支持加密压缩
- **实时监控**: 用户行为监控、系统状态监控
- **缩略图支持**: JPG、PSD、TIF、AI、EPS、PNG等格式缩略图

### 安全功能
- **用户认证**: 分离的管理员和用户登录系统
- **访问控制**: IP访问控制、网络环境检测
- **行为监控**: 详细的用户操作日志记录
- **加密下载**: 可配置的文件加密下载
- **限流保护**: 防止恶意下载和访问

### 管理功能
- **用户管理**: 用户创建、编辑、权限分配
- **系统配置**: 灵活的系统参数配置
- **日志管理**: 完整的操作日志和审计功能
- **通知系统**: 系统公告和滚动通知
- **远程管理**: 支持远程系统管理

## 🛠️ 技术栈

- **后端**: Python 3.7+ + Flask + MySQL
- **前端**: HTML5 + CSS3 + JavaScript (原生)
- **图像处理**: OpenCV + Pillow
- **数据库**: MySQL 5.7+
- **安全**: Flask-Login + 自定义安全策略

## 📋 系统要求

### 软件要求
- Python 3.7 或更高版本
- MySQL 5.7 或更高版本
- Windows 10 或更高版本

### 硬件要求
- CPU: 双核 2.0GHz 或更高
- 内存: 4GB RAM 或更高
- 存储: 10GB 可用空间
- 网络: 100Mbps 或更高（推荐）

## 🚀 快速开始

### 1. 环境准备

确保已安装以下软件：
- Python 3.7+
- MySQL 5.7+
- Git (可选)

### 2. 下载项目

```bash
# 如果使用Git
git clone <repository-url>
cd Net

# 或直接下载解压到目录
```

### 3. 配置数据库

1. 启动MySQL服务
2. 确保MySQL用户名为 `root`，密码为 `123456`
3. 或修改 `backend/config.py` 中的数据库配置

### 4. 一键启动

```bash
python start.py
```

启动脚本会自动：
- 检查Python版本和MySQL连接
- 安装所需依赖包
- 初始化数据库
- 创建必要目录
- 启动系统

### 5. 访问系统

- **管理员登录**: http://localhost:5000/admin
  - 用户名: `admin`
  - 密码: `admin123`

- **用户登录**: http://localhost:5000/
  - 需要管理员创建用户账户

## 📁 项目结构

```
Net/
├── backend/                 # 后端代码
│   ├── app.py              # Flask主应用
│   ├── config.py           # 配置文件
│   ├── models/             # 数据库模型
│   │   ├── __init__.py
│   │   ├── user.py         # 用户模型
│   │   ├── file_info.py    # 文件信息模型
│   │   ├── permission.py   # 权限模型
│   │   ├── log.py          # 日志模型
│   │   └── system_config.py # 系统配置模型
│   ├── routes/             # API路由
│   │   ├── __init__.py
│   │   ├── admin_routes.py # 管理员路由
│   │   ├── user_routes.py  # 用户路由
│   │   └── api_routes.py   # 通用API路由
│   ├── services/           # 业务逻辑
│   └── utils/              # 工具函数
│       ├── decorators.py   # 装饰器
│       ├── security.py     # 安全工具
│       └── logger.py       # 日志工具
├── frontend/               # 前端代码
│   ├── admin/              # 管理员界面
│   │   ├── login.html      # 管理员登录
│   │   └── dashboard.html  # 管理员控制台
│   ├── user/               # 用户界面
│   │   └── login.html      # 用户登录
│   └── static/             # 静态资源
│       ├── css/            # 样式文件
│       ├── js/             # JavaScript文件
│       └── images/         # 图片资源
├── database/               # 数据库脚本
│   └── init_db.sql         # 数据库初始化脚本
├── search_engines/         # 搜索引擎
├── image_recognition/      # 图像识别模块
├── requirements.txt        # Python依赖
├── start.py               # 启动脚本
└── README.md              # 项目说明
```

## ⚙️ 配置说明

### 数据库配置

编辑 `backend/config.py` 文件：

```python
# 数据库配置
MYSQL_HOST = 'localhost'
MYSQL_PORT = 3306
MYSQL_USER = 'root'
MYSQL_PASSWORD = '123456'
MYSQL_DATABASE = 'file_sharing_system'
```

### 系统配置

主要配置项在 `backend/config.py` 中：

- `MAX_CONTENT_LENGTH`: 最大上传文件大小
- `SUPPORTED_IMAGE_FORMATS`: 支持的图片格式
- `MAX_BATCH_DOWNLOAD_SIZE`: 批量下载最大大小
- `ENCRYPTION_THRESHOLD`: 加密下载阈值
- `INTERNAL_NETWORKS`: 内网IP段定义

## 👥 用户管理

### 管理员功能
- 用户创建和管理
- 权限分配
- 系统配置
- 日志查看
- 文件管理

### 用户组权限
- **管理员组**: 所有权限
- **默认用户组**: 基础读取和下载权限
- **高级用户组**: 包含上传权限
- **只读用户组**: 仅浏览和搜索权限

## 🔍 搜索功能

### Everything风格搜索
- 快速文件名搜索
- 支持通配符
- 实时索引更新

### 图像识别搜索
- 基于OpenCV的图像分析
- 支持相似图片搜索
- 智能标签识别

## 📊 监控和日志

### 用户行为监控
- 登录/退出记录
- 文件访问记录
- 下载行为记录
- 搜索行为记录

### 系统监控
- 在线用户统计
- 系统资源监控
- 错误日志记录
- 性能指标统计

## 🔒 安全特性

### 访问控制
- 内网/外网访问控制
- IP白名单/黑名单
- 会话管理
- 防暴力破解

### 数据保护
- 密码加密存储
- 文件加密下载
- 安全传输
- 审计日志

## 🚀 部署说明

### 开发环境
```bash
python start.py
```

### 生产环境
1. 修改配置文件中的安全设置
2. 使用WSGI服务器（如Gunicorn）
3. 配置反向代理（如Nginx）
4. 设置SSL证书
5. 配置防火墙规则

## 🔧 故障排除

### 常见问题

1. **MySQL连接失败**
   - 检查MySQL服务是否启动
   - 验证用户名密码是否正确
   - 确认防火墙设置

2. **依赖安装失败**
   - 升级pip: `python -m pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

3. **权限问题**
   - 确保Python有足够的文件系统权限
   - 检查目录创建权限

4. **端口占用**
   - 修改 `backend/app.py` 中的端口配置
   - 或终止占用端口的进程

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的用户管理系统
- 基础文件共享功能
- 管理员控制台
- 安全认证系统

## 📄 许可证

本项目为企业内部使用，保证原创无侵权。

## 🤝 技术支持

如有问题请联系IT部门或系统管理员。

---

**企业文件共享系统 v1.0.0**  
*安全 · 高效 · 便捷的企业文件共享解决方案*
