/* 用户登录页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 420px;
    padding: 20px;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

.logo img {
    width: 64px;
    height: 64px;
    margin-bottom: 12px;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.logo h1 {
    color: #2d3436;
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 8px;
}

.subtitle {
    color: #636e72;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
}

.login-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2d3436;
    font-weight: 500;
    font-size: 14px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 14px 45px 14px 16px;
    border: 2px solid #ddd6fe;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
    color: #2d3436;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #74b9ff;
    box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
}

.input-icon {
    position: absolute;
    right: 16px;
    color: #b2bec3;
    font-size: 18px;
    pointer-events: none;
}

.toggle-password {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: #b2bec3;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #74b9ff;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
}

.remember-me input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #74b9ff;
}

.remember-me label {
    color: #636e72;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 0;
}

.forgot-password {
    color: #74b9ff;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #0984e3;
}

.login-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(116, 185, 255, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: none;
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #fcc;
    font-size: 14px;
    text-align: center;
    margin-top: 15px;
}

.login-footer {
    text-align: center;
}

.features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #636e72;
    font-size: 13px;
}

.feature-icon {
    font-size: 16px;
}

.register-section {
    margin-bottom: 20px;
}

.register-section p {
    color: #636e72;
    font-size: 14px;
}

.register-section a {
    color: #74b9ff;
    text-decoration: none;
    font-weight: 500;
}

.register-section a:hover {
    color: #0984e3;
}

.system-info p {
    color: #b2bec3;
    font-size: 12px;
}

/* 背景装饰 */
.bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 100px;
    height: 100px;
    border-radius: 20px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    border-radius: 30px;
    top: 30%;
    right: 5%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
    }
}

/* 系统公告 */
.system-announcement {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 12px 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
    max-width: 90%;
}

.announcement-content {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2d3436;
    font-size: 14px;
}

.announcement-icon {
    font-size: 16px;
}

.announcement-text {
    animation: marquee 15s linear infinite;
    white-space: nowrap;
}

@keyframes marquee {
    0% { transform: translateX(0); }
    100% { transform: translateX(-20px); }
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: white;
    font-size: 16px;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal {
    background: white;
    border-radius: 15px;
    max-width: 450px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #f1f2f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3436;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #b2bec3;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #2d3436;
}

.modal-body {
    padding: 25px;
}

.modal-body .form-group {
    margin-bottom: 20px;
}

.modal-body input,
.modal-body textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd6fe;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.modal-body input:focus,
.modal-body textarea:focus {
    outline: none;
    border-color: #74b9ff;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #f1f2f6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
}

.btn-secondary {
    background: #b2bec3;
    color: white;
}

.btn-secondary:hover {
    background: #636e72;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 15px;
    }
    
    .login-box {
        padding: 30px 25px;
    }
    
    .logo h1 {
        font-size: 22px;
    }
    
    .features {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .system-announcement {
        bottom: 10px;
        padding: 10px 15px;
    }
}
