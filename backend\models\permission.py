from datetime import datetime
from . import db

class Permission(db.Model):
    """权限模型"""
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(50))  # 权限分类：file, system, user等
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'created_at': self.created_at.isoformat()
        }

# 用户组权限关联表
group_permissions = db.Table('group_permissions',
    db.Column('group_id', db.Integer, db.ForeignKey('user_groups.id'), primary_key=True),
    db.Column('permission_id', db.Integer, db.<PERSON><PERSON>ey('permissions.id'), primary_key=True)
)

class UserGroupPermission(db.Model):
    """用户组权限关联模型"""
    __tablename__ = 'user_group_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.Integer, db.ForeignKey('user_groups.id'), nullable=False)
    permission_name = db.Column(db.String(100), nullable=False)
    permission_value = db.Column(db.Boolean, default=True)
    
    # 权限参数（JSON格式存储额外配置）
    permission_params = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'group_id': self.group_id,
            'permission_name': self.permission_name,
            'permission_value': self.permission_value,
            'permission_params': self.permission_params,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class DirectoryPermission(db.Model):
    """目录权限模型"""
    __tablename__ = 'directory_permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    shared_dir_id = db.Column(db.Integer, db.ForeignKey('shared_directories.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('user_groups.id'), nullable=False)
    
    # 目录级别权限
    can_read = db.Column(db.Boolean, default=True)
    can_download = db.Column(db.Boolean, default=True)
    can_upload = db.Column(db.Boolean, default=False)
    can_delete = db.Column(db.Boolean, default=False)
    can_modify = db.Column(db.Boolean, default=False)
    
    # 搜索权限
    can_search = db.Column(db.Boolean, default=True)
    can_use_image_search = db.Column(db.Boolean, default=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    shared_directory = db.relationship('SharedDirectory', backref='directory_permissions', lazy=True)
    user_group = db.relationship('UserGroup', backref='directory_permissions', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'shared_dir_id': self.shared_dir_id,
            'group_id': self.group_id,
            'can_read': self.can_read,
            'can_download': self.can_download,
            'can_upload': self.can_upload,
            'can_delete': self.can_delete,
            'can_modify': self.can_modify,
            'can_search': self.can_search,
            'can_use_image_search': self.can_use_image_search,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
