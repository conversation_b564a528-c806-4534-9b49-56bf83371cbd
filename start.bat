@echo off
chcp 65001 >nul
title 企业文件共享系统启动器

echo ================================================
echo 企业文件共享系统 - Windows启动器
echo ================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python未安装或未添加到PATH
    echo 请安装Python 3.7或更高版本
    pause
    exit /b 1
)

python --version
echo ✓ Python环境正常
echo.

echo 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ✗ 依赖包安装失败
    pause
    exit /b 1
)
echo ✓ 依赖包安装完成
echo.

echo 正在创建必要目录...
if not exist "uploads" mkdir uploads
if not exist "thumbnails" mkdir thumbnails
if not exist "temp_downloads" mkdir temp_downloads
if not exist "logs" mkdir logs
if not exist "search_index" mkdir search_index
if not exist "models" mkdir models
echo ✓ 目录创建完成
echo.

echo 正在启动系统...
echo.
echo 默认管理员账户:
echo 用户名: admin
echo 密码: admin123
echo.
echo 访问地址:
echo 管理员: http://localhost:5000/admin
echo 用户: http://localhost:5000/
echo.
echo 按 Ctrl+C 停止系统
echo ================================================
echo.

cd backend
python app.py

pause
