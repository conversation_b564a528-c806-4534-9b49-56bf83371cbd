from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from . import db, login_manager

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(100), nullable=True)
    
    # 用户类型：admin(管理员), user(普通用户)
    user_type = db.Column(db.String(20), nullable=False, default='user')
    
    # 用户组ID
    group_id = db.Column(db.Integer, db.<PERSON>('user_groups.id'), nullable=True)
    
    # 状态：active(活跃), banned(禁用), locked(锁定)
    status = db.Column(db.String(20), nullable=False, default='active')
    
    # 禁用到期时间
    ban_until = db.Column(db.DateTime, nullable=True)
    
    # 登录相关
    last_login = db.Column(db.DateTime, nullable=True)
    login_count = db.Column(db.Integer, default=0)
    failed_login_count = db.Column(db.Integer, default=0)
    last_failed_login = db.Column(db.DateTime, nullable=True)
    
    # 下载统计
    download_count = db.Column(db.Integer, default=0)
    download_size = db.Column(db.BigInteger, default=0)  # 字节
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    group = db.relationship('UserGroup', backref='users', lazy=True)
    logs = db.relationship('UserLog', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """是否为管理员"""
        return self.user_type == 'admin'
    
    def is_active_user(self):
        """用户是否处于活跃状态"""
        if self.status == 'banned':
            if self.ban_until and self.ban_until > datetime.utcnow():
                return False
            elif not self.ban_until:
                return False
        return self.status == 'active'
    
    def get_permissions(self):
        """获取用户权限"""
        if not self.group:
            return []
        return self.group.permissions
    
    def has_permission(self, permission_name):
        """检查用户是否有特定权限"""
        if self.is_admin():
            return True
        permissions = self.get_permissions()
        return any(p.name == permission_name for p in permissions)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'user_type': self.user_type,
            'group_id': self.group_id,
            'group_name': self.group.name if self.group else None,
            'status': self.status,
            'ban_until': self.ban_until.isoformat() if self.ban_until else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_count': self.login_count,
            'download_count': self.download_count,
            'download_size': self.download_size,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class UserGroup(db.Model):
    """用户组模型"""
    __tablename__ = 'user_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # 基础权限
    can_read = db.Column(db.Boolean, default=True)
    can_download = db.Column(db.Boolean, default=True)
    can_upload = db.Column(db.Boolean, default=False)
    can_delete = db.Column(db.Boolean, default=False)
    can_modify = db.Column(db.Boolean, default=False)
    
    # 搜索权限
    can_search = db.Column(db.Boolean, default=True)
    can_use_image_search = db.Column(db.Boolean, default=True)
    
    # 网络访问权限
    internal_access = db.Column(db.Boolean, default=True)
    external_access = db.Column(db.Boolean, default=False)
    
    # 下载限制
    max_download_size = db.Column(db.BigInteger, default=100*1024*1024)  # 100MB
    max_downloads_per_day = db.Column(db.Integer, default=50)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'can_read': self.can_read,
            'can_download': self.can_download,
            'can_upload': self.can_upload,
            'can_delete': self.can_delete,
            'can_modify': self.can_modify,
            'can_search': self.can_search,
            'can_use_image_search': self.can_use_image_search,
            'internal_access': self.internal_access,
            'external_access': self.external_access,
            'max_download_size': self.max_download_size,
            'max_downloads_per_day': self.max_downloads_per_day,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

@login_manager.user_loader
def load_user(user_id):
    """Flask-Login用户加载器"""
    return User.query.get(int(user_id))
