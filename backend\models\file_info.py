from datetime import datetime
from . import db

class SharedDirectory(db.Model):
    """共享目录模型"""
    __tablename__ = 'shared_directories'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    path = db.Column(db.String(500), nullable=False)
    description = db.Column(db.Text)
    
    # 访问权限
    internal_access = db.Column(db.Bo<PERSON>, default=True)
    external_access = db.Column(db.Bo<PERSON>, default=False)
    
    # 功能权限
    allow_read = db.Column(db.<PERSON>, default=True)
    allow_download = db.Column(db.<PERSON>, default=True)
    allow_upload = db.Column(db.Bo<PERSON>, default=False)
    allow_delete = db.Column(db.Bo<PERSON>an, default=False)
    allow_modify = db.Column(db.<PERSON>, default=False)
    
    # 搜索设置
    enable_search = db.Column(db.<PERSON>, default=True)
    enable_image_search = db.Column(db.<PERSON>, default=True)
    
    # 状态
    status = db.Column(db.String(20), default='active')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    files = db.relationship('FileInfo', backref='shared_directory', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'path': self.path,
            'description': self.description,
            'internal_access': self.internal_access,
            'external_access': self.external_access,
            'allow_read': self.allow_read,
            'allow_download': self.allow_download,
            'allow_upload': self.allow_upload,
            'allow_delete': self.allow_delete,
            'allow_modify': self.allow_modify,
            'enable_search': self.enable_search,
            'enable_image_search': self.enable_image_search,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class FileInfo(db.Model):
    """文件信息模型"""
    __tablename__ = 'file_info'
    
    id = db.Column(db.BigInteger, primary_key=True)
    shared_dir_id = db.Column(db.Integer, db.ForeignKey('shared_directories.id'), nullable=False)
    file_path = db.Column(db.String(1000), nullable=False)
    file_name = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.BigInteger, nullable=False)
    file_type = db.Column(db.String(50))
    file_extension = db.Column(db.String(20))
    
    # 文件哈希
    file_hash = db.Column(db.String(64))
    
    # 图片相关
    is_image = db.Column(db.Boolean, default=False)
    image_width = db.Column(db.Integer)
    image_height = db.Column(db.Integer)
    has_thumbnail = db.Column(db.Boolean, default=False)
    
    # 索引状态
    indexed = db.Column(db.Boolean, default=False)
    image_analyzed = db.Column(db.Boolean, default=False)
    
    # 时间信息
    file_mtime = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'shared_dir_id': self.shared_dir_id,
            'file_path': self.file_path,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'file_hash': self.file_hash,
            'is_image': self.is_image,
            'image_width': self.image_width,
            'image_height': self.image_height,
            'has_thumbnail': self.has_thumbnail,
            'indexed': self.indexed,
            'image_analyzed': self.image_analyzed,
            'file_mtime': self.file_mtime.isoformat() if self.file_mtime else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
