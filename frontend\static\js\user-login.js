// 用户登录页面JavaScript
class UserLogin {
    constructor() {
        this.form = document.getElementById('userLoginForm');
        this.loginBtn = document.getElementById('loginBtn');
        this.errorMessage = document.getElementById('errorMessage');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        
        this.init();
    }
    
    init() {
        // 绑定表单提交事件
        this.form.addEventListener('submit', (e) => this.handleLogin(e));
        
        // 绑定回车键登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !document.getElementById('registerModal').style.display.includes('flex')) {
                this.handleLogin(e);
            }
        });
        
        // 清除错误信息
        const inputs = this.form.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => this.clearError());
        });
        
        // 检查是否已登录
        this.checkLoginStatus();
        
        // 加载系统公告
        this.loadSystemAnnouncement();
    }
    
    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(this.form);
        const loginData = {
            username: formData.get('username').trim(),
            password: formData.get('password'),
            remember: formData.get('remember') === 'on'
        };
        
        // 基础验证
        if (!this.validateForm(loginData)) {
            return;
        }
        
        try {
            this.setLoading(true);
            
            const response = await fetch('/api/user/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(loginData)
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                this.showSuccess('登录成功，正在进入系统...');
                
                // 保存登录状态
                if (loginData.remember) {
                    localStorage.setItem('user_remember', 'true');
                }
                
                // 跳转到用户主页
                setTimeout(() => {
                    window.location.href = '/user/dashboard';
                }, 1000);
                
            } else {
                this.showError(result.message || '登录失败，请检查用户名和密码');
                
                // 如果是账户被锁定，显示特殊信息
                if (result.code === 'ACCOUNT_LOCKED') {
                    this.showError(`账户已被锁定，解锁时间：${result.unlock_time}`);
                } else if (result.code === 'ACCOUNT_BANNED') {
                    this.showError('账户已被禁用，请联系管理员');
                }
            }
            
        } catch (error) {
            console.error('登录请求失败:', error);
            this.showError('网络连接失败，请检查网络后重试');
        } finally {
            this.setLoading(false);
        }
    }
    
    validateForm(data) {
        if (!data.username) {
            this.showError('请输入用户名');
            document.getElementById('username').focus();
            return false;
        }
        
        if (data.username.length < 3) {
            this.showError('用户名长度不能少于3位');
            document.getElementById('username').focus();
            return false;
        }
        
        if (!data.password) {
            this.showError('请输入密码');
            document.getElementById('password').focus();
            return false;
        }
        
        if (data.password.length < 6) {
            this.showError('密码长度不能少于6位');
            document.getElementById('password').focus();
            return false;
        }
        
        return true;
    }
    
    setLoading(loading) {
        if (loading) {
            this.loginBtn.disabled = true;
            this.loginBtn.querySelector('.btn-text').style.display = 'none';
            this.loginBtn.querySelector('.btn-loading').style.display = 'inline';
            this.loadingOverlay.style.display = 'flex';
        } else {
            this.loginBtn.disabled = false;
            this.loginBtn.querySelector('.btn-text').style.display = 'inline';
            this.loginBtn.querySelector('.btn-loading').style.display = 'none';
            this.loadingOverlay.style.display = 'none';
        }
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.errorMessage.style.background = '#fee';
        this.errorMessage.style.color = '#c33';
        
        // 5秒后自动隐藏
        setTimeout(() => {
            this.clearError();
        }, 5000);
    }
    
    showSuccess(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.errorMessage.style.background = '#efe';
        this.errorMessage.style.color = '#3c3';
    }
    
    clearError() {
        this.errorMessage.style.display = 'none';
    }
    
    async checkLoginStatus() {
        try {
            const response = await fetch('/api/user/check-login', {
                method: 'GET',
                credentials: 'include'
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.logged_in) {
                    // 已登录，直接跳转
                    window.location.href = '/user/dashboard';
                }
            }
        } catch (error) {
            // 忽略检查登录状态的错误
            console.log('检查登录状态失败:', error);
        }
    }
    
    async loadSystemAnnouncement() {
        try {
            const response = await fetch('/api/system/announcement', {
                method: 'GET'
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.announcement) {
                    document.querySelector('.announcement-text').textContent = result.announcement;
                }
            }
        } catch (error) {
            console.log('加载系统公告失败:', error);
        }
    }
}

// 密码显示/隐藏切换
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.textContent = '🙈';
    } else {
        passwordInput.type = 'password';
        toggleBtn.textContent = '👁️';
    }
}

// 显示忘记密码
function showForgotPassword() {
    alert('请联系系统管理员重置密码');
}

// 显示注册模态框
function showRegister() {
    document.getElementById('registerModal').style.display = 'flex';
}

// 关闭注册模态框
function closeRegisterModal() {
    document.getElementById('registerModal').style.display = 'none';
    document.getElementById('registerForm').reset();
}

// 提交注册申请
async function submitRegister() {
    const form = document.getElementById('registerForm');
    const formData = new FormData(form);
    
    const registerData = {
        username: formData.get('username').trim(),
        real_name: formData.get('real_name').trim(),
        email: formData.get('email').trim(),
        reason: formData.get('reason').trim()
    };
    
    // 基础验证
    if (!registerData.username || registerData.username.length < 3) {
        alert('请输入有效的用户名（至少3位）');
        return;
    }
    
    if (!registerData.real_name) {
        alert('请输入真实姓名');
        return;
    }
    
    if (!registerData.email || !isValidEmail(registerData.email)) {
        alert('请输入有效的邮箱地址');
        return;
    }
    
    if (!registerData.reason) {
        alert('请填写申请理由');
        return;
    }
    
    try {
        const response = await fetch('/api/user/register-request', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(registerData)
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            alert('注册申请已提交，请等待管理员审核。审核结果将通过邮件通知您。');
            closeRegisterModal();
        } else {
            alert(result.message || '提交申请失败，请稍后重试');
        }
        
    } catch (error) {
        console.error('提交注册申请失败:', error);
        alert('网络连接失败，请稍后重试');
    }
}

// 邮箱验证
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 点击模态框外部关闭
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal-overlay')) {
        closeRegisterModal();
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new UserLogin();
});

// 防止页面被嵌入iframe
if (window.top !== window.self) {
    window.top.location = window.location;
}
