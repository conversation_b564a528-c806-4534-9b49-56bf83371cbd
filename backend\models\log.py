from datetime import datetime
from . import db

class UserLog(db.Model):
    """用户日志模型"""
    __tablename__ = 'user_logs'
    
    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    
    # 文件相关
    file_id = db.Column(db.BigInteger, db.<PERSON>ey('file_info.id'))
    file_path = db.Column(db.String(1000))
    file_size = db.Column(db.BigInteger)
    
    # 搜索相关
    search_query = db.Column(db.String(500))
    search_results_count = db.Column(db.Integer)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'description': self.description,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'file_id': self.file_id,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'search_query': self.search_query,
            'search_results_count': self.search_results_count,
            'created_at': self.created_at.isoformat()
        }

class DownloadRecord(db.Model):
    """下载记录模型"""
    __tablename__ = 'download_records'
    
    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    file_id = db.Column(db.BigInteger, db.ForeignKey('file_info.id'))
    file_path = db.Column(db.String(1000), nullable=False)
    file_size = db.Column(db.BigInteger, nullable=False)
    download_type = db.Column(db.String(20), nullable=False)  # single, batch, folder
    
    # 加密信息
    is_encrypted = db.Column(db.Boolean, default=False)
    encryption_password = db.Column(db.String(255))
    
    # 网络信息
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    
    # 状态
    status = db.Column(db.String(20), default='completed')  # completed, failed, cancelled
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    user = db.relationship('User', backref='download_records', lazy=True)
    file = db.relationship('FileInfo', backref='download_records', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'file_id': self.file_id,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'download_type': self.download_type,
            'is_encrypted': self.is_encrypted,
            'encryption_password': self.encryption_password,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'status': self.status,
            'created_at': self.created_at.isoformat()
        }
