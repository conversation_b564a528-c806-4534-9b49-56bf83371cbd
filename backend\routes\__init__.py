"""
路由模块初始化
"""

from .admin_routes import admin_bp
from .user_routes import user_bp
from .api_routes import api_bp

def register_routes(app):
    """注册所有路由"""
    
    # 注册管理员路由
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    
    # 注册用户路由
    app.register_blueprint(user_bp, url_prefix='/api/user')
    
    # 注册通用API路由
    app.register_blueprint(api_bp, url_prefix='/api')
    
    app.logger.info('所有路由注册完成')
