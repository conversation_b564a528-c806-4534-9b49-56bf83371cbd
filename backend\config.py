import os
from datetime import timedelta

class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'enterprise-file-sharing-system-2024'
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or '123456'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'file_sharing_system'
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {'charset': 'utf8mb4'}
    }
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 1024 * 1024 * 1024  # 1GB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'uploads')
    
    # 缩略图配置
    THUMBNAIL_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'thumbnails')
    THUMBNAIL_SIZES = {
        'small': (150, 150),
        'medium': (300, 300),
        'large': (600, 600),
        'xlarge': (1200, 1200)
    }
    
    # 支持的图片格式
    SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.psd', '.ai', '.eps']
    
    # 搜索引擎配置
    SEARCH_INDEX_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'search_index')
    
    # 下载配置
    DOWNLOAD_TEMP_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'temp_downloads')
    MAX_BATCH_DOWNLOAD_SIZE = 500 * 1024 * 1024  # 500MB
    MAX_BATCH_DOWNLOAD_COUNT = 100
    
    # 加密下载配置
    ENCRYPTION_THRESHOLD = 5  # 下载5次后开始加密
    DEFAULT_ZIP_PASSWORD = 'FileShare2024'
    
    # 网络访问配置
    INTERNAL_NETWORKS = ['192.168.', '10.', '172.16.', '172.17.', '172.18.', '172.19.', '172.20.', '172.21.', '172.22.', '172.23.', '172.24.', '172.25.', '172.26.', '172.27.', '172.28.', '172.29.', '172.30.', '172.31.']
    EXTERNAL_ACCESS_ENABLED = True
    
    # 监控配置
    LOG_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'logs')
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # 限流配置
    RATE_LIMIT_ENABLED = True
    MAX_REQUESTS_PER_MINUTE = 60
    MAX_DOWNLOADS_PER_HOUR = 50
    
    # 图像识别配置
    IMAGE_RECOGNITION_ENABLED = True
    IMAGE_RECOGNITION_MODEL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'models')

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
