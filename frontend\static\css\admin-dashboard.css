/* 管理员控制台样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background: #f5f7fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* 顶部导航栏 */
.top-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.system-name {
    font-size: 18px;
    font-weight: 600;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #27ae60;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #27ae60;
}

.status-dot.offline {
    background: #e74c3c;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.header-right {
    display: flex;
    align-items: center;
}

.admin-info {
    position: relative;
}

.admin-name {
    margin-right: 10px;
    font-size: 14px;
}

.admin-menu {
    position: relative;
}

.admin-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.admin-avatar:hover {
    background: rgba(255, 255, 255, 0.3);
}

.admin-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    min-width: 150px;
    padding: 8px 0;
    display: none;
    z-index: 1001;
}

.admin-dropdown.show {
    display: block;
}

.admin-dropdown a {
    display: block;
    padding: 8px 16px;
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    transition: background 0.3s ease;
}

.admin-dropdown a:hover {
    background: #f8f9fa;
}

.admin-dropdown a.logout {
    color: #e74c3c;
}

.admin-dropdown hr {
    margin: 5px 0;
    border: none;
    border-top: 1px solid #eee;
}

/* 主容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 侧边栏 */
.sidebar {
    width: 260px;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 30px;
}

.nav-section h3 {
    color: #7f8c8d;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0 20px;
    margin-bottom: 10px;
}

.nav-section ul {
    list-style: none;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-item a {
    display: block;
    padding: 12px 20px;
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item a:hover {
    background: #f8f9fa;
    color: #667eea;
}

.nav-item.active a {
    background: #667eea;
    color: white;
    border-left-color: #4a69bd;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 260px;
    padding: 30px;
    background: #f5f7fa;
}

.content-page {
    display: none;
}

.content-page.active {
    display: block;
}

.page-header {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.page-header p {
    color: #7f8c8d;
    font-size: 14px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.stat-info h3 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #7f8c8d;
    font-size: 14px;
}

/* 监控区域 */
.monitor-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.monitor-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.monitor-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1f2f6;
}

.activity-list, .online-users-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item, .user-item {
    padding: 10px 0;
    border-bottom: 1px solid #f1f2f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item:last-child, .user-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: #7f8c8d;
    font-size: 12px;
}

.activity-desc {
    color: #2c3e50;
    font-size: 14px;
}

.user-name {
    color: #2c3e50;
    font-size: 14px;
}

/* 通知区域 */
.notification-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.notification-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.notification-marquee {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    overflow: hidden;
}

.marquee-content {
    color: #2c3e50;
    font-size: 14px;
    animation: marquee 20s linear infinite;
    white-space: nowrap;
}

@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

/* 加载占位符 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #7f8c8d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f1f2f6;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #f1f2f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #7f8c8d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #f1f2f6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .monitor-section {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}
